# 🎯 Waitlist Button - Shopify Theme Integration

A beautiful, fully-functional waitlist button that can be embedded in Shopify product pages to capture customer emails when products are out of stock.

## 🚀 **Features**

### ✅ **Smart Display Logic**
- **Auto-Detection** - Only shows when product is out of stock
- **Inventory Aware** - Checks both Shopify-managed and manual inventory
- **Variant Support** - Works with product variants
- **In-Stock Message** - Shows positive message when product is available

### ✅ **Beautiful UI Design**
- **Gradient Background** - Modern purple gradient design
- **Responsive Layout** - Works on desktop and mobile
- **Smooth Animations** - Hover effects and loading states
- **Interactive Elements** - Focus states and button feedback
- **Success/Error States** - Clear user feedback

### ✅ **Robust Functionality**
- **Email Validation** - Client-side and server-side validation
- **Duplicate Prevention** - Prevents duplicate waitlist entries
- **Loading States** - Visual feedback during API calls
- **Error Handling** - Graceful error recovery
- **CORS Support** - Works across different domains

## 📁 **Files Structure**

```
extensions/waitlist-button/blocks/
└── star_rating.liquid          # Main waitlist button component

app/routes/
└── api.public.waitlist.jsx     # Public API endpoint for storefront

Documentation/
└── WAITLIST_BUTTON_README.md   # This file
```

## 🎨 **Visual Design**

### **Out of Stock (Waitlist Shown):**
```
┌─────────────────────────────────────┐
│        Join the Waitlist           │
│  Be the first to know when this    │
│     item is back in stock!         │
│                                     │
│  ┌─────────────────────────────┐   │
│  │ Enter your email address... │   │
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │     JOIN THE WAITLIST       │   │
│  └─────────────────────────────┘   │
└─────────────────────────────────────┘
```

### **In Stock (Success Message):**
```
┌─────────────────────────────────────┐
│  ✓ In Stock - Ready to Ship!       │
└─────────────────────────────────────┘
```

## 🔧 **Technical Implementation**

### **Liquid Template Logic**
```liquid
{% assign current_variant = product.selected_or_first_available_variant %}
{% assign show_waitlist = false %}

{% if current_variant.inventory_management == 'shopify' and current_variant.inventory_quantity <= 0 %}
  {% assign show_waitlist = true %}
{% elsif current_variant.inventory_management != 'shopify' and current_variant.available == false %}
  {% assign show_waitlist = true %}
{% endif %}
```

### **JavaScript API Integration**
```javascript
const response = await fetch('{{ shop.url }}/apps/api/public/waitlist', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  },
  body: JSON.stringify({
    action: 'create_waitlist',
    data: {
      email: email,
      productVariantId: productVariantId,
      priority: 'Medium',
      shop: '{{ shop.permanent_domain }}'
    }
  })
});
```

### **CSS Styling (Inline)**
- **Gradient Background**: `linear-gradient(135deg, #667eea 0%, #764ba2 100%)`
- **Border Radius**: `12px` for modern rounded corners
- **Box Shadow**: `0 8px 32px rgba(0, 0, 0, 0.1)` for depth
- **Transitions**: `all 0.3s ease` for smooth interactions
- **Responsive**: Media queries for mobile optimization

## 🔄 **Data Flow**

```
1. Customer visits product page
   ↓
2. Liquid checks inventory status
   ↓
3. If out of stock → Show waitlist form
   ↓
4. Customer enters email and submits
   ↓
5. JavaScript validates email format
   ↓
6. API call to /apps/api/public/waitlist
   ↓
7. Server validates and saves to database
   ↓
8. Success/error message displayed
   ↓
9. Form hidden on success
```

## 🛠️ **Installation Steps**

### **Step 1: Add to Theme**
1. Go to Shopify Admin → Online Store → Themes
2. Click "Actions" → Edit code
3. Navigate to `sections/` or `blocks/` folder
4. Create new file: `waitlist-button.liquid`
5. Copy the content from `star_rating.liquid`

### **Step 2: Include in Product Template**
Add to your product template:
```liquid
{% render 'waitlist-button' %}
```

Or use as a section:
```liquid
{% section 'waitlist-button' %}
```

### **Step 3: Configure API Endpoint**
Ensure your Shopify app is running with the public API endpoint at:
```
/apps/api/public/waitlist
```

## ⚙️ **Configuration Options**

### **Customize Colors**
```css
/* Change gradient colors */
background: linear-gradient(135deg, #your-color-1 0%, #your-color-2 100%);

/* Change button color */
background: #your-button-color;
color: #your-text-color;
```

### **Customize Text**
```liquid
<!-- Change heading -->
<h3>Your Custom Heading</h3>

<!-- Change description -->
<p>Your custom description text</p>

<!-- Change button text -->
<span>Your Button Text</span>
```

### **Customize Behavior**
```javascript
// Change priority level
priority: 'High' // or 'Medium', 'Low'

// Add custom validation
if (!email.includes('@yourcompany.com')) {
  showError('Please use your company email');
  return;
}
```

## 🔒 **Security Features**

### **Email Validation**
- **Client-side**: Regex validation before submission
- **Server-side**: Additional validation in API
- **Format Check**: Standard email format validation

### **CORS Protection**
- **Cross-Origin Headers**: Proper CORS configuration
- **Domain Validation**: Shop domain verification
- **Request Validation**: Method and content-type checks

### **Data Sanitization**
- **Input Cleaning**: Trim and validate all inputs
- **SQL Injection Prevention**: Prisma ORM protection
- **XSS Prevention**: Proper data escaping

## 📊 **Analytics & Tracking**

### **Built-in Logging**
```javascript
console.log('Product Variant ID:', productVariantId);
console.error('Waitlist error:', error);
```

### **Custom Event Tracking**
Add Google Analytics or other tracking:
```javascript
// After successful submission
gtag('event', 'waitlist_join', {
  'product_id': productVariantId,
  'email_domain': email.split('@')[1]
});
```

## 🎯 **Best Practices**

### **Performance**
- **Inline Styles**: Reduces external CSS requests
- **Minimal JavaScript**: Lightweight implementation
- **Conditional Loading**: Only loads when needed

### **User Experience**
- **Clear Messaging**: Obvious call-to-action
- **Immediate Feedback**: Loading and success states
- **Error Recovery**: Helpful error messages
- **Mobile Friendly**: Responsive design

### **SEO Considerations**
- **Semantic HTML**: Proper form structure
- **Accessibility**: ARIA labels and keyboard navigation
- **Progressive Enhancement**: Works without JavaScript

## 🐛 **Troubleshooting**

### **Button Not Showing**
1. Check product inventory status
2. Verify variant availability
3. Check console for JavaScript errors
4. Ensure API endpoint is accessible

### **API Errors**
1. Verify app is running
2. Check CORS configuration
3. Validate shop domain
4. Check database connection

### **Styling Issues**
1. Check for CSS conflicts
2. Verify inline styles are applied
3. Test on different devices
4. Check browser compatibility

## 🎉 **Success Metrics**

Track these KPIs to measure success:
- **Waitlist Conversion Rate**: Emails captured / Page views
- **Restock Notification Success**: Customers notified / Total waitlist
- **Purchase Conversion**: Sales from waitlist / Notifications sent
- **User Engagement**: Time spent on product page

## 🚀 **Future Enhancements**

Potential improvements:
- **SMS Notifications**: Add phone number collection
- **Priority Levels**: Let customers choose priority
- **Estimated Restock**: Show expected availability date
- **Social Sharing**: Share waitlist with friends
- **Bulk Import**: CSV upload for existing customers

The waitlist button provides a complete solution for capturing customer interest when products are out of stock! 🎊
