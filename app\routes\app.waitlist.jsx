import { useState, useCallback } from "react";
import { use<PERSON><PERSON><PERSON>, useLoaderData } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Button,
  BlockStack,
  Text,
  InlineStack,
  Badge,
  EmptyState,
  Banner,
  TextField,
  Select,
  Modal,
  FormLayout,
  DataTable,
  Pagination,
} from "@shopify/polaris";
import { TitleBar, useAppBridge } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";
import prisma from "../db.server";

export const loader = async ({ request }) => {
  const { session } = await authenticate.admin(request);
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get("page") || "1");
  const limit = 10;
  const skip = (page - 1) * limit;

  // Get waitlists with product variant information
  const waitlists = await prisma.waitlist.findMany({
    where: {
      shop: session.shop,
    },
    include: {
      productVariant: true,
    },
    orderBy: {
      createdAt: 'desc',
    },
    skip,
    take: limit,
  });

  // Get total count for pagination
  const totalCount = await prisma.waitlist.count({
    where: {
      shop: session.shop,
    },
  });

  // Get available product variants for the dropdown
  const productVariants = await prisma.productVariant.findMany({
    where: {
      shop: session.shop,
      enabled: true,
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  return {
    waitlists,
    productVariants,
    pagination: {
      currentPage: page,
      totalPages: Math.ceil(totalCount / limit),
      totalCount,
      hasNext: page < Math.ceil(totalCount / limit),
      hasPrevious: page > 1,
    },
  };
};

export const action = async ({ request }) => {
  const { session } = await authenticate.admin(request);
  const formData = await request.formData();
  const actionType = formData.get("action");

  if (actionType === "create_waitlist") {
    const email = formData.get("email");
    const productVariantId = formData.get("productVariantId");
    const priority = formData.get("priority");

    try {
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return {
          success: false,
          message: "Please enter a valid email address.",
        };
      }

      // Check if waitlist entry already exists
      const existingWaitlist = await prisma.waitlist.findFirst({
        where: {
          email: email,
          productVariantId: BigInt(productVariantId),
          shop: session.shop,
        },
      });

      if (existingWaitlist) {
        return {
          success: false,
          message: "This email is already on the waitlist for this product variant.",
        };
      }

      // Create new waitlist entry
      const newWaitlist = await prisma.waitlist.create({
        data: {
          email: email,
          productVariantId: BigInt(productVariantId),
          priority: priority,
          shop: session.shop,
        },
        include: {
          productVariant: true,
        },
      });

      return {
        success: true,
        message: `Waitlist entry created successfully for ${email}!`,
        waitlist: newWaitlist,
      };
    } catch (error) {
      console.error("Error creating waitlist:", error);
      return {
        success: false,
        message: "Failed to create waitlist entry. Please try again.",
        error: error.message,
      };
    }
  }

  if (actionType === "update_waitlist") {
    const waitlistId = formData.get("waitlistId");
    const email = formData.get("email");
    const priority = formData.get("priority");

    try {
      const updatedWaitlist = await prisma.waitlist.update({
        where: {
          id: waitlistId,
          shop: session.shop,
        },
        data: {
          email: email,
          priority: priority,
        },
        include: {
          productVariant: true,
        },
      });

      return {
        success: true,
        message: "Waitlist entry updated successfully!",
        waitlist: updatedWaitlist,
      };
    } catch (error) {
      console.error("Error updating waitlist:", error);
      return {
        success: false,
        message: "Failed to update waitlist entry. Please try again.",
      };
    }
  }

  if (actionType === "delete_waitlist") {
    const waitlistId = formData.get("waitlistId");

    try {
      await prisma.waitlist.delete({
        where: {
          id: waitlistId,
          shop: session.shop,
        },
      });

      return {
        success: true,
        message: "Waitlist entry deleted successfully!",
      };
    } catch (error) {
      console.error("Error deleting waitlist:", error);
      return {
        success: false,
        message: "Failed to delete waitlist entry. Please try again.",
      };
    }
  }

  return { success: false, message: "Invalid action" };
};

export default function WaitlistManager() {
  const { waitlists, productVariants, pagination } = useLoaderData();
  const fetcher = useFetcher();
  const shopify = useAppBridge();
  
  // Form state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingWaitlist, setEditingWaitlist] = useState(null);
  const [formData, setFormData] = useState({
    email: "",
    productVariantId: "",
    priority: "Medium",
  });

  const isLoading = fetcher.state === "submitting";

  // Priority options
  const priorityOptions = [
    { label: "High", value: "High" },
    { label: "Medium", value: "Medium" },
    { label: "Low", value: "Low" },
  ];

  // Product variant options
  const variantOptions = [
    { label: "Select a product variant", value: "" },
    ...productVariants.map((variant) => ({
      label: `Variant ID: ${variant.id} (Product: ${variant.productId})`,
      value: variant.id.toString(),
    })),
  ];

  const handleOpenModal = (waitlist = null) => {
    if (waitlist) {
      setEditingWaitlist(waitlist);
      setFormData({
        email: waitlist.email,
        productVariantId: waitlist.productVariantId.toString(),
        priority: waitlist.priority,
      });
    } else {
      setEditingWaitlist(null);
      setFormData({
        email: "",
        productVariantId: "",
        priority: "Medium",
      });
    }
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingWaitlist(null);
    setFormData({
      email: "",
      productVariantId: "",
      priority: "Medium",
    });
  };

  const handleSubmit = () => {
    const submitFormData = new FormData();
    submitFormData.append("action", editingWaitlist ? "update_waitlist" : "create_waitlist");
    submitFormData.append("email", formData.email);
    submitFormData.append("productVariantId", formData.productVariantId);
    submitFormData.append("priority", formData.priority);
    
    if (editingWaitlist) {
      submitFormData.append("waitlistId", editingWaitlist.id);
    }

    fetcher.submit(submitFormData, { method: "post" });
    handleCloseModal();
  };

  const handleDelete = (waitlistId) => {
    const submitFormData = new FormData();
    submitFormData.append("action", "delete_waitlist");
    submitFormData.append("waitlistId", waitlistId);
    
    fetcher.submit(submitFormData, { method: "post" });
  };

  // Show success/error messages
  if (fetcher.data?.message) {
    shopify.toast.show(fetcher.data.message, { 
      isError: !fetcher.data.success 
    });
  }

  // Prepare data for DataTable
  const tableRows = waitlists.map((waitlist) => [
    waitlist.email,
    `Variant: ${waitlist.productVariantId}`,
    `Product: ${waitlist.productVariant.productId}`,
    <Badge key={waitlist.id} status={
      waitlist.priority === "High" ? "critical" : 
      waitlist.priority === "Medium" ? "attention" : "info"
    }>
      {waitlist.priority}
    </Badge>,
    new Date(waitlist.createdAt).toLocaleDateString(),
    <InlineStack key={waitlist.id} gap="200">
      <Button size="micro" onClick={() => handleOpenModal(waitlist)}>
        Edit
      </Button>
      <Button 
        size="micro" 
        destructive 
        onClick={() => handleDelete(waitlist.id)}
        loading={isLoading}
      >
        Delete
      </Button>
    </InlineStack>,
  ]);

  return (
    <Page>
      <TitleBar title="Waitlist Manager" />
      
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <InlineStack align="space-between">
                <Text as="h2" variant="headingMd">
                  Waitlist Entries ({pagination.totalCount})
                </Text>
                <Button
                  primary
                  onClick={() => handleOpenModal()}
                  disabled={productVariants.length === 0}
                >
                  Add to Waitlist
                </Button>
              </InlineStack>

              {productVariants.length === 0 && (
                <Banner status="warning">
                  <Text as="p">
                    No product variants available. Please add some products first using the Product Selector.
                  </Text>
                </Banner>
              )}

              {fetcher.data?.success === false && (
                <Banner status="critical">
                  <Text as="p">{fetcher.data.message}</Text>
                </Banner>
              )}

              {waitlists.length === 0 ? (
                <EmptyState
                  heading="No waitlist entries yet"
                  image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
                >
                  <Text as="p">Create your first waitlist entry to get started.</Text>
                </EmptyState>
              ) : (
                <BlockStack gap="400">
                  <DataTable
                    columnContentTypes={['text', 'text', 'text', 'text', 'text', 'text']}
                    headings={['Email', 'Variant ID', 'Product ID', 'Priority', 'Created', 'Actions']}
                    rows={tableRows}
                  />
                  
                  {pagination.totalPages > 1 && (
                    <InlineStack align="center">
                      <Pagination
                        hasPrevious={pagination.hasPrevious}
                        onPrevious={() => {
                          const url = new URL(window.location);
                          url.searchParams.set('page', (pagination.currentPage - 1).toString());
                          window.location.href = url.toString();
                        }}
                        hasNext={pagination.hasNext}
                        onNext={() => {
                          const url = new URL(window.location);
                          url.searchParams.set('page', (pagination.currentPage + 1).toString());
                          window.location.href = url.toString();
                        }}
                        label={`Page ${pagination.currentPage} of ${pagination.totalPages}`}
                      />
                    </InlineStack>
                  )}
                </BlockStack>
              )}
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>

      {/* Create/Edit Modal */}
      <Modal
        open={isModalOpen}
        onClose={handleCloseModal}
        title={editingWaitlist ? "Edit Waitlist Entry" : "Add to Waitlist"}
        primaryAction={{
          content: editingWaitlist ? "Update" : "Create",
          onAction: handleSubmit,
          loading: isLoading,
          disabled: !formData.email || !formData.productVariantId,
        }}
        secondaryActions={[
          {
            content: "Cancel",
            onAction: handleCloseModal,
          },
        ]}
      >
        <Modal.Section>
          <FormLayout>
            <TextField
              label="Email Address"
              value={formData.email}
              onChange={(value) => setFormData({ ...formData, email: value })}
              type="email"
              autoComplete="email"
              placeholder="<EMAIL>"
              helpText="Customer's email address for waitlist notifications"
            />
            
            <Select
              label="Product Variant"
              options={variantOptions}
              value={formData.productVariantId}
              onChange={(value) => setFormData({ ...formData, productVariantId: value })}
              disabled={editingWaitlist !== null}
              helpText={editingWaitlist ? "Product variant cannot be changed" : "Select the product variant for this waitlist"}
            />
            
            <Select
              label="Priority"
              options={priorityOptions}
              value={formData.priority}
              onChange={(value) => setFormData({ ...formData, priority: value })}
              helpText="Priority level for this waitlist entry"
            />
          </FormLayout>
        </Modal.Section>
      </Modal>
    </Page>
  );
}
