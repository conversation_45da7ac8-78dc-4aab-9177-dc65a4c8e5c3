/**
 * Shopify AI Chatbot Widget
 * Embeddable chatbot for Shopify stores
 */

class ShopifyChatbotWidget {
    constructor(config = {}) {
        this.config = {
            apiEndpoint: config.apiEndpoint || '/api/chatbot',
            shopDomain: config.shopDomain || window.location.hostname,
            title: config.title || 'Chat with us',
            welcomeMessage: config.welcomeMessage || 'Hello! How can I help you today?',
            primaryColor: config.primaryColor || '#667eea',
            position: config.position || 'bottom-right',
            autoOpen: config.autoOpen || false,
            showNotification: config.showNotification !== false,
            ...config
        };

        this.isOpen = false;
        this.sessionId = null;
        this.messages = [];
        this.isTyping = false;

        this.init();
    }

    init() {
        this.createWidget();
        this.bindEvents();
        this.loadSession();
        this.extractPageContext();
        
        if (this.config.autoOpen) {
            setTimeout(() => this.openChat(), 1000);
        }
        
        if (this.config.showNotification) {
            this.showNotification();
        }
    }

    createWidget() {
        // Widget is already in HTML, just customize it
        const widget = document.getElementById('shopify-chatbot-widget');
        if (!widget) {
            console.error('Chatbot widget container not found');
            return;
        }

        // Set title and welcome message
        const title = document.getElementById('chatbot-title');
        const welcomeText = document.getElementById('welcome-text');
        
        if (title) title.textContent = this.config.title;
        if (welcomeText) welcomeText.textContent = this.config.welcomeMessage;

        // Apply custom colors
        this.applyCustomColors();
        
        // Position widget
        this.positionWidget();
    }

    applyCustomColors() {
        const style = document.createElement('style');
        style.textContent = `
            .chatbot-toggle {
                background: linear-gradient(135deg, ${this.config.primaryColor} 0%, ${this.adjustColor(this.config.primaryColor, -20)} 100%) !important;
            }
            .chatbot-header {
                background: linear-gradient(135deg, ${this.config.primaryColor} 0%, ${this.adjustColor(this.config.primaryColor, -20)} 100%) !important;
            }
            .user-message .message-content {
                background: linear-gradient(135deg, ${this.config.primaryColor} 0%, ${this.adjustColor(this.config.primaryColor, -20)} 100%) !important;
            }
            .chatbot-send {
                background: linear-gradient(135deg, ${this.config.primaryColor} 0%, ${this.adjustColor(this.config.primaryColor, -20)} 100%) !important;
            }
            .chatbot-input:focus {
                border-color: ${this.config.primaryColor} !important;
            }
        `;
        document.head.appendChild(style);
    }

    adjustColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }

    positionWidget() {
        const widget = document.getElementById('shopify-chatbot-widget');
        if (!widget) return;

        const positions = {
            'bottom-right': { bottom: '20px', right: '20px' },
            'bottom-left': { bottom: '20px', left: '20px' },
            'top-right': { top: '20px', right: '20px' },
            'top-left': { top: '20px', left: '20px' }
        };

        const pos = positions[this.config.position] || positions['bottom-right'];
        Object.assign(widget.style, pos);
    }

    bindEvents() {
        const toggle = document.getElementById('chatbot-toggle');
        const close = document.getElementById('chatbot-close');
        const input = document.getElementById('chatbot-input');
        const send = document.getElementById('chatbot-send');

        if (toggle) {
            toggle.addEventListener('click', () => this.toggleChat());
        }

        if (close) {
            close.addEventListener('click', () => this.closeChat());
        }

        if (input) {
            input.addEventListener('input', (e) => this.handleInputChange(e));
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
            
            // Auto-resize textarea
            input.addEventListener('input', () => {
                input.style.height = 'auto';
                input.style.height = Math.min(input.scrollHeight, 100) + 'px';
            });
        }

        if (send) {
            send.addEventListener('click', () => this.sendMessage());
        }

        // Close on outside click
        document.addEventListener('click', (e) => {
            const widget = document.getElementById('shopify-chatbot-widget');
            if (widget && !widget.contains(e.target) && this.isOpen) {
                this.closeChat();
            }
        });

        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.closeChat();
            }
        });
    }

    extractPageContext() {
        this.pageContext = {
            url: window.location.href,
            path: window.location.pathname,
            title: document.title,
            shop: this.config.shopDomain
        };

        // Extract product information if on product page
        if (window.location.pathname.includes('/products/')) {
            this.extractProductData();
        }

        // Extract collection information if on collection page
        if (window.location.pathname.includes('/collections/')) {
            this.extractCollectionData();
        }
    }

    extractProductData() {
        try {
            // Try multiple methods to get product data
            let productData = {};

            // Method 1: Look for Shopify product JSON
            const productScript = document.querySelector('script[data-product-json]');
            if (productScript) {
                productData = JSON.parse(productScript.textContent);
            }

            // Method 2: Look for meta tags
            const titleMeta = document.querySelector('meta[property="og:title"]');
            const priceMeta = document.querySelector('meta[property="product:price:amount"]');
            const imageMeta = document.querySelector('meta[property="og:image"]');

            if (titleMeta) productData.title = titleMeta.content;
            if (priceMeta) productData.price = priceMeta.content;
            if (imageMeta) productData.image = imageMeta.content;

            // Method 3: Extract from page elements
            if (!productData.title) {
                const titleElement = document.querySelector('h1, .product-title, [data-product-title]');
                if (titleElement) productData.title = titleElement.textContent.trim();
            }

            if (!productData.price) {
                const priceElement = document.querySelector('.price, [data-price], .product-price');
                if (priceElement) productData.price = priceElement.textContent.trim();
            }

            this.pageContext.product = productData;
        } catch (error) {
            console.warn('Could not extract product data:', error);
        }
    }

    extractCollectionData() {
        try {
            const collectionTitle = document.querySelector('h1, .collection-title')?.textContent?.trim();
            if (collectionTitle) {
                this.pageContext.collection = { title: collectionTitle };
            }
        } catch (error) {
            console.warn('Could not extract collection data:', error);
        }
    }

    toggleChat() {
        if (this.isOpen) {
            this.closeChat();
        } else {
            this.openChat();
        }
    }

    openChat() {
        const container = document.getElementById('chatbot-container');
        const toggle = document.getElementById('chatbot-toggle');
        
        if (container && toggle) {
            container.style.display = 'flex';
            toggle.classList.add('active');
            this.isOpen = true;
            
            // Focus on input
            const input = document.getElementById('chatbot-input');
            if (input) {
                setTimeout(() => input.focus(), 100);
            }
            
            // Hide notification
            this.hideNotification();
            
            // Track event
            this.trackEvent('chat_opened');
        }
    }

    closeChat() {
        const container = document.getElementById('chatbot-container');
        const toggle = document.getElementById('chatbot-toggle');
        
        if (container && toggle) {
            container.style.display = 'none';
            toggle.classList.remove('active');
            this.isOpen = false;
            
            // Track event
            this.trackEvent('chat_closed');
        }
    }

    handleInputChange(e) {
        const send = document.getElementById('chatbot-send');
        if (send) {
            send.disabled = !e.target.value.trim();
        }
    }

    async sendMessage() {
        const input = document.getElementById('chatbot-input');
        const message = input?.value?.trim();
        
        if (!message || this.isTyping) return;

        // Add user message to chat
        this.addMessage(message, 'user');
        
        // Clear input
        input.value = '';
        input.style.height = 'auto';
        this.handleInputChange({ target: { value: '' } });
        
        // Show typing indicator
        this.showTyping();
        
        try {
            const response = await this.callAPI(message);
            this.hideTyping();
            
            if (response.error) {
                this.addMessage(response.error, 'error');
            } else {
                this.addMessage(response.response, 'bot');
                if (response.sessionId) {
                    this.sessionId = response.sessionId;
                    this.saveSession();
                }
            }
            
            // Track message
            this.trackEvent('message_sent', { message_length: message.length });
            
        } catch (error) {
            this.hideTyping();
            this.addMessage('Sorry, I encountered an error. Please try again.', 'error');
            console.error('Chatbot API error:', error);
        }
    }

    async callAPI(message) {
        const payload = {
            message: message,
            sessionId: this.sessionId,
            context: this.pageContext
        };

        const response = await fetch(this.config.apiEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    addMessage(content, type) {
        const messagesContainer = document.getElementById('chatbot-messages');
        if (!messagesContainer) return;

        // Remove welcome message if it exists
        const welcomeMessage = messagesContainer.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = `chatbot-message ${type}-message`;
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.textContent = content;
        
        const timeDiv = document.createElement('div');
        timeDiv.className = 'message-time';
        timeDiv.textContent = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        
        messageDiv.appendChild(contentDiv);
        messageDiv.appendChild(timeDiv);
        messagesContainer.appendChild(messageDiv);
        
        // Store message
        this.messages.push({ content, type, timestamp: new Date().toISOString() });
        
        // Scroll to bottom
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        // Save to session
        this.saveSession();
    }

    showTyping() {
        const typing = document.getElementById('chatbot-typing');
        if (typing) {
            typing.style.display = 'flex';
            this.isTyping = true;
        }
    }

    hideTyping() {
        const typing = document.getElementById('chatbot-typing');
        if (typing) {
            typing.style.display = 'none';
            this.isTyping = false;
        }
    }

    showNotification() {
        const notification = document.getElementById('chatbot-notification');
        if (notification) {
            notification.style.display = 'flex';
        }
    }

    hideNotification() {
        const notification = document.getElementById('chatbot-notification');
        if (notification) {
            notification.style.display = 'none';
        }
    }

    loadSession() {
        try {
            const saved = localStorage.getItem('shopify-chatbot-session');
            if (saved) {
                const session = JSON.parse(saved);
                this.sessionId = session.sessionId;
                this.messages = session.messages || [];
                
                // Restore messages
                this.messages.forEach(msg => {
                    this.addMessage(msg.content, msg.type);
                });
            }
        } catch (error) {
            console.warn('Could not load chat session:', error);
        }
    }

    saveSession() {
        try {
            const session = {
                sessionId: this.sessionId,
                messages: this.messages.slice(-20), // Keep last 20 messages
                timestamp: new Date().toISOString()
            };
            localStorage.setItem('shopify-chatbot-session', JSON.stringify(session));
        } catch (error) {
            console.warn('Could not save chat session:', error);
        }
    }

    trackEvent(eventName, data = {}) {
        // Track events for analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', eventName, {
                event_category: 'chatbot',
                ...data
            });
        }
        
        // Custom tracking
        if (this.config.onEvent) {
            this.config.onEvent(eventName, data);
        }
    }

    // Public API methods
    open() {
        this.openChat();
    }

    close() {
        this.closeChat();
    }

    sendCustomMessage(message) {
        this.addMessage(message, 'bot');
    }

    clearChat() {
        const messagesContainer = document.getElementById('chatbot-messages');
        if (messagesContainer) {
            messagesContainer.innerHTML = `
                <div class="welcome-message">
                    <span class="icon">🤖</span>
                    <div>
                        <strong>Hello! I'm your AI assistant</strong><br>
                        <span>${this.config.welcomeMessage}</span>
                    </div>
                </div>
            `;
        }
        this.messages = [];
        this.sessionId = null;
        this.saveSession();
    }
}

// Auto-initialize if config is provided
if (typeof window !== 'undefined') {
    window.ShopifyChatbotWidget = ShopifyChatbotWidget;
    
    // Auto-initialize with global config
    if (window.shopifyChatbotConfig) {
        window.shopifyChatbot = new ShopifyChatbotWidget(window.shopifyChatbotConfig);
    }
}
