import { json } from "@remix-run/node";
import prisma from "../db.server";

export const action = async ({ request }) => {
  // Set CORS headers for cross-origin requests from Shopify storefront
  const headers = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "POST, OPTIONS, GET",
    "Access-Control-Allow-Headers": "Content-Type, X-Requested-With, Accept",
    "Content-Type": "application/json",
  };

  // Handle preflight OPTIONS request
  if (request.method === "OPTIONS") {
    return new Response(null, { status: 200, headers });
  }

  console.log("App proxy request received:", {
    method: request.method,
    url: request.url,
    headers: Object.fromEntries(request.headers.entries())
  });

  if (request.method === "POST") {
    try {
      const body = await request.json();
      console.log("App proxy request body:", body);
      
      const { 
        email, 
        productVariantId, 
        priority = "Medium", 
        shop,
        productId,
        productTitle,
        variantTitle,
        timestamp
      } = body;

      // Validate required fields
      if (!email || !productVariantId) {
        return Response.json({ 
          success: false,
          error: "Email and product variant ID are required" 
        }, { status: 400, headers });
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return Response.json({ 
          success: false,
          error: "Please enter a valid email address" 
        }, { status: 400, headers });
      }

      // Extract shop domain from request or use provided shop
      const referrer = request.headers.get("referer") || "";
      const shopDomain = shop || extractShopFromUrl(referrer) || "productionbit.myshopify.com";

      console.log("Using shop domain:", shopDomain);

      // Check if email is already on waitlist for this variant
      const existingEntry = await prisma.waitlist.findFirst({
        where: {
          email: email,
          productVariantId: BigInt(productVariantId),
          shop: shopDomain,
        },
      });

      if (existingEntry) {
        return Response.json({ 
          success: true,
          message: "You're already on the waitlist for this product!",
          alreadyExists: true,
          waitlistId: existingEntry.id
        }, { headers });
      }

      // Create new waitlist entry
      const newWaitlist = await prisma.waitlist.create({
        data: {
          email: email,
          productVariantId: BigInt(productVariantId),
          priority: priority,
          shop: shopDomain,
        },
      });

      console.log("Waitlist entry created:", newWaitlist);

      return Response.json({
        success: true,
        message: "Successfully added to waitlist! We'll notify you when it's available.",
        waitlistId: newWaitlist.id,
        data: {
          email: email,
          productVariantId: productVariantId,
          priority: priority,
          shop: shopDomain,
          productId: productId,
          productTitle: productTitle,
          variantTitle: variantTitle,
          createdAt: newWaitlist.createdAt
        }
      }, { headers });

    } catch (error) {
      console.error("Error creating waitlist entry:", error);
      return Response.json({ 
        success: false,
        error: "Failed to join waitlist. Please try again.",
        details: error.message
      }, { status: 500, headers });
    }
  }

  return Response.json({ 
    success: false,
    error: "Method not allowed" 
  }, { status: 405, headers });
};

// GET method for health check and testing
export const loader = async ({ request }) => {
  const headers = {
    "Access-Control-Allow-Origin": "*",
    "Content-Type": "application/json",
  };

  console.log("App proxy GET request:", {
    url: request.url,
    headers: Object.fromEntries(request.headers.entries())
  });

  return Response.json({ 
    status: "ok", 
    service: "waitlist-app-proxy",
    endpoint: "/apps/wait-wise",
    timestamp: new Date().toISOString(),
    message: "Waitlist app proxy is working!"
  }, { headers });
};

// Extract shop domain from URL
function extractShopFromUrl(url) {
  try {
    if (!url) return null;
    
    const urlObj = new URL(url);
    const hostname = urlObj.hostname;
    
    // Handle myshopify.com domains
    if (hostname.includes('.myshopify.com')) {
      return hostname;
    }
    
    // Handle custom domains - you might need to add logic here
    // to map custom domains to shop domains
    return hostname;
  } catch (error) {
    console.error("Error extracting shop from URL:", error);
    return null;
  }
}
