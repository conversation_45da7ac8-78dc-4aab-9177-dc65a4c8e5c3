import { v4 as uuidv4 } from "uuid";
import { ChatGroq } from "@langchain/groq";
import {
  StateGraph,
  MessagesAnnotation,
  END,
  START,
} from "@langchain/langgraph";
import { HumanMessage } from "@langchain/core/messages";
import prisma from "../db.server";

// Initialize Groq chat model
const model = new ChatGroq({
  apiKey: process.env.GROQ_API_KEY, // Make sure to set this in your environment variables
  modelName: "llama3-70b-8192", // Groq supports various models like llama3-70b-8192, mixtral-8x7b, etc.
  temperature: 0.7,
});

// Define the function that calls the Groq model
const callModel = async (state, config) => {
  if (!config.configurable?.sessionId) {
    throw new Error("Session ID is required");
  }

  const { sessionId, shopId, context } = config.configurable;

  // Get chat history from database
  let chatHistory = await prisma.chatSession.findUnique({
    where: { id: sessionId },
    include: { messages: { orderBy: { createdAt: 'asc' } } }
  });

  // Create new session if it doesn't exist
  if (!chatHistory) {
    chatHistory = await prisma.chatSession.create({
      data: {
        id: sessionId,
        shopId: shopId,
        messages: {
          create: state.messages.map(msg => ({
            role: msg.type === 'human' ? 'user' : 'assistant',
            content: msg.content,
          }))
        }
      },
      include: { messages: true }
    });
  } else if (state.messages.length > 0) {
    // Add new message to existing session
    await prisma.chatMessage.create({
      data: {
        sessionId: sessionId,
        role: state.messages[0].type === 'human' ? 'user' : 'assistant',
        content: state.messages[0].content,
      }
    });
  }

  // Format messages for the model
  const formattedMessages = chatHistory.messages.map(msg => {
    return msg.role === 'user'
      ? new HumanMessage(msg.content)
      : { type: 'ai', content: msg.content };
  });

  // Add current message if not already in history
  const allMessages = [...formattedMessages, ...state.messages];

  // Build context-aware system message
  let systemContent = "You are a helpful assistant for a Shopify store. Provide concise, accurate information about products, orders, and general e-commerce questions.";

  // Add product context if available
  if (context?.product) {
    const product = context.product;
    systemContent += `\n\nCurrent product context:
- Product: ${product.title || 'Unknown'}
- Price: ${product.price || 'Not specified'}
- Description: ${product.description || 'No description available'}`;

    if (product.variants && product.variants.length > 0) {
      systemContent += `\n- Available variants: ${product.variants.map(v => v.title).join(', ')}`;
    }
  }

  // Add page context
  if (context?.page) {
    if (context.page.includes('/products/')) {
      systemContent += "\n\nThe customer is currently viewing a product page. Focus on helping them with product-related questions, sizing, availability, shipping, and purchasing decisions.";
    } else if (context.page.includes('/cart')) {
      systemContent += "\n\nThe customer is on the cart page. Help them with checkout process, shipping options, or product modifications.";
    } else if (context.page.includes('/collections/')) {
      systemContent += "\n\nThe customer is browsing a product collection. Help them find products that match their needs.";
    }
  }

  systemContent += "\n\nAlways be helpful, friendly, and provide accurate information. If you don't know something specific about the store's policies or products, suggest they contact customer service for detailed information.";

  const systemMessage = {
    role: "system",
    content: systemContent
  };

  // Call Groq API
  const aiMessage = await model.invoke(allMessages, {
    system: systemMessage.content
  });

  // Save assistant's response to database
  await prisma.chatMessage.create({
    data: {
      sessionId: sessionId,
      role: 'assistant',
      content: aiMessage.content,
    }
  });

  return { messages: [aiMessage] };
};

// Define the graph
const createChatbotGraph = () => {
  const workflow = new StateGraph(MessagesAnnotation)
    .addNode("model", callModel)
    .addEdge(START, "model")
    .addEdge("model", END);

  return workflow.compile();
};

// Export the chatbot functionality
export const createChatSession = (shopId) => {
  const app = createChatbotGraph();
  const sessionId = uuidv4();

  return {
    sessionId,
    sendMessage: async (message) => {
      const config = {
        configurable: { sessionId, shopId },
        streamMode: "values"
      };

      const inputMessage = new HumanMessage(message);
      const responses = [];

      for await (const event of await app.stream(
        { messages: [inputMessage] },
        config
      )) {
        const lastMessage = event.messages[event.messages.length - 1];
        responses.push(lastMessage.content);
      }

      return responses[responses.length - 1];
    }
  };
};

// Create storefront chat session with context support
export const createStorefrontChatSession = (shopId, existingSessionId = null) => {
  const app = createChatbotGraph();
  const sessionId = existingSessionId || uuidv4();

  return {
    sessionId,
    sendMessage: async (message, context = null) => {
      const config = {
        configurable: { sessionId, shopId, context },
        streamMode: "values"
      };

      const inputMessage = new HumanMessage(message);
      const responses = [];

      for await (const event of await app.stream(
        { messages: [inputMessage] },
        config
      )) {
        const lastMessage = event.messages[event.messages.length - 1];
        responses.push(lastMessage.content);
      }

      return responses[responses.length - 1];
    }
  };
};

// Get existing chat sessions for a shop
export const getShopChatSessions = async (shopId) => {
  return prisma.chatSession.findMany({
    where: { shopId },
    include: { 
      messages: {
        orderBy: { createdAt: 'asc' },
        take: 1 // Just get the first message to show as preview
      }
    },
    orderBy: { updatedAt: 'desc' }
  });
};

// Get a specific chat session with all messages
export const getChatSession = async (sessionId) => {
  return prisma.chatSession.findUnique({
    where: { id: sessionId },
    include: { 
      messages: {
        orderBy: { createdAt: 'asc' }
      }
    }
  });
};
