# 🤖 Shopify AI Chatbot - Integration Guide

Add an intelligent AI chatbot to your Shopify store in just a few minutes!

## 🚀 Quick Setup (2 minutes)

### Method 1: Simple Script Integration

Add this code to your theme's `theme.liquid` file before the closing `</body>` tag:

```html
<!-- Shopify AI Chatbot -->
<script>
  window.shopifyChatbotConfig = {
    apiEndpoint: 'YOUR_APP_URL/api/chatbot',
    shopDomain: '{{ shop.permanent_domain }}',
    title: 'Chat with us',
    welcomeMessage: 'Hello! How can I help you today?',
    primaryColor: '#667eea',
    position: 'bottom-right',
    autoOpen: false,
    showNotification: true
  };
</script>
<script src="YOUR_APP_URL/shopify-chatbot-embed.js"></script>
```

**Replace `YOUR_APP_URL`** with your actual app URL (e.g., `https://your-app.herokuapp.com`)

### Method 2: Theme Extension (Recommended)

1. Install the app from the Shopify App Store
2. Go to your theme editor
3. Add the "AI Chatbot Widget" block to any section
4. Customize the settings in the theme editor

## ⚙️ Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `apiEndpoint` | string | `/api/chatbot` | Your app's chatbot API endpoint |
| `shopDomain` | string | auto-detected | Your shop domain |
| `title` | string | `'Chat with us'` | Widget header title |
| `welcomeMessage` | string | `'Hello! How can I help you today?'` | First message shown |
| `primaryColor` | string | `'#667eea'` | Main color for buttons and headers |
| `position` | string | `'bottom-right'` | Widget position (`bottom-right`, `bottom-left`, `top-right`, `top-left`) |
| `autoOpen` | boolean | `false` | Automatically open chat after page load |
| `showNotification` | boolean | `true` | Show notification badge on chat button |
| `enabled` | boolean | `true` | Enable/disable the widget |

## 🎨 Customization Examples

### Custom Colors
```javascript
window.shopifyChatbotConfig = {
  apiEndpoint: 'YOUR_APP_URL/api/chatbot',
  shopDomain: '{{ shop.permanent_domain }}',
  primaryColor: '#ff6b6b', // Red theme
  title: 'Need Help?',
  welcomeMessage: 'Hi there! I\'m here to help you find the perfect product!'
};
```

### Different Position
```javascript
window.shopifyChatbotConfig = {
  apiEndpoint: 'YOUR_APP_URL/api/chatbot',
  shopDomain: '{{ shop.permanent_domain }}',
  position: 'bottom-left', // Left side
  autoOpen: true // Opens automatically
};
```

### Mobile-Optimized
```javascript
window.shopifyChatbotConfig = {
  apiEndpoint: 'YOUR_APP_URL/api/chatbot',
  shopDomain: '{{ shop.permanent_domain }}',
  // Mobile users often prefer auto-open
  autoOpen: window.innerWidth <= 768,
  showNotification: true
};
```

## 📱 Features

### ✅ What the Chatbot Can Do:

- **Product Questions**: Answer questions about specific products
- **Store Information**: Provide store policies, shipping info, etc.
- **Product Recommendations**: Suggest products based on customer needs
- **Order Support**: Help with order status and issues
- **General Assistance**: Answer common customer questions

### 🧠 AI Capabilities:

- **Context Aware**: Understands what page the customer is viewing
- **Product Smart**: Knows about the current product being viewed
- **Conversation Memory**: Remembers the conversation context
- **Natural Language**: Understands natural human language
- **Fast Responses**: Powered by Groq's ultra-fast LLama3-70B model

## 🛠️ Advanced Integration

### Custom Event Tracking
```javascript
window.shopifyChatbotConfig = {
  apiEndpoint: 'YOUR_APP_URL/api/chatbot',
  shopDomain: '{{ shop.permanent_domain }}',
  onEvent: function(eventName, data) {
    // Track chatbot events
    console.log('Chatbot event:', eventName, data);
    
    // Send to Google Analytics
    if (typeof gtag !== 'undefined') {
      gtag('event', eventName, {
        event_category: 'chatbot',
        ...data
      });
    }
  }
};
```

### Programmatic Control
```javascript
// Open chat programmatically
window.ShopifyChatbot.instance?.open();

// Close chat
window.ShopifyChatbot.instance?.close();

// Send custom message
window.ShopifyChatbot.instance?.sendCustomMessage('Welcome to our store!');

// Clear chat history
window.ShopifyChatbot.instance?.clearChat();
```

### Conditional Loading
```javascript
// Only show chatbot on certain pages
if (window.location.pathname.includes('/products/') || 
    window.location.pathname.includes('/collections/')) {
  window.shopifyChatbotConfig = {
    apiEndpoint: 'YOUR_APP_URL/api/chatbot',
    shopDomain: '{{ shop.permanent_domain }}',
    enabled: true
  };
}
```

## 🎯 Best Practices

### 1. **Positioning**
- `bottom-right`: Most common, doesn't interfere with navigation
- `bottom-left`: Good for RTL languages or if you have right-side elements
- Mobile: Widget automatically adapts to mobile screens

### 2. **Colors**
- Use your brand colors for consistency
- Ensure good contrast for accessibility
- Test on both light and dark backgrounds

### 3. **Messages**
- Keep welcome message friendly and helpful
- Mention specific ways the bot can help
- Use your brand voice and tone

### 4. **Performance**
- Script loads asynchronously, won't slow down your site
- Widget only loads when needed
- Conversations are cached locally

## 🔧 Troubleshooting

### Widget Not Appearing
1. Check that the script URL is correct
2. Verify `shopifyChatbotConfig` is defined before the script loads
3. Check browser console for JavaScript errors
4. Ensure `enabled: true` in config

### API Errors
1. Verify the `apiEndpoint` URL is correct
2. Check that CORS is properly configured
3. Ensure the Groq API key is set in your app environment

### Styling Issues
1. Check for CSS conflicts with your theme
2. Use browser dev tools to inspect the widget
3. The widget uses high z-index (9999) to appear on top

### Mobile Issues
1. Widget automatically adapts to mobile screens
2. On small screens, it becomes full-width
3. Test on actual devices, not just browser resize

## 📞 Support

If you need help with integration:

1. **Check the browser console** for error messages
2. **Test the API endpoint** directly to ensure it's working
3. **Verify your configuration** matches the examples above
4. **Contact support** through the app admin panel

## 🚀 Go Live!

Once you've added the code to your theme:

1. **Save your theme** changes
2. **Test the chatbot** on your live store
3. **Try different types of questions** to see how it responds
4. **Monitor conversations** through the app admin panel
5. **Customize settings** based on customer feedback

Your AI chatbot is now ready to help your customers 24/7! 🎉
