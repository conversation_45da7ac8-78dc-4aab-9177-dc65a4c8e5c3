import { useEffect, useState } from "react";
import { useFetcher } from "@remix-run/react";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  Box,
  List,
  Link,
  InlineStack,
  TextField,
} from "@shopify/polaris";
import { TitleBar, useAppBridge } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }) => {
  await authenticate.admin(request);

  return null;
};

export const action = async ({ request }) => {
  const { admin } = await authenticate.admin(request);
  const formData = await request.formData();
  const action = formData.get("action");

  if (action === "getProducts") {
    // Get existing products with optional search
    const searchQuery = formData.get("searchQuery");
    const categoryQuery = formData.get("categoryQuery");

    // Build search query - combine name and category if both provided
    let finalQuery = null;
    if (searchQuery && categoryQuery) {
      finalQuery = `title:*${searchQuery}* AND product_type:*${categoryQuery}*`;
    } else if (searchQuery) {
      finalQuery = `title:*${searchQuery}*`;
    } else if (categoryQuery) {
      finalQuery = `product_type:*${categoryQuery}*`;
    }

    const response = await admin.graphql(
      `#graphql
        query getProducts($first: Int!, $query: String) {
          products(first: $first, query: $query) {
            edges {
              node {
                id
                title
                handle
                status
                createdAt
                updatedAt
                totalInventory
                vendor
                productType
                priceRangeV2 {
                  minVariantPrice {
                    amount
                    currencyCode
                  }
                  maxVariantPrice {
                    amount
                    currencyCode
                  }
                }
                variants(first: 5) {
                  edges {
                    node {
                      id
                      title
                      price
                      inventoryQuantity
                      availableForSale
                    }
                  }
                }
                images(first: 1) {
                  edges {
                    node {
                      url
                      altText
                    }
                  }
                }
              }
            }
            pageInfo {
              hasNextPage
              hasPreviousPage
            }
          }
        }`,
      {
        variables: {
          first: 250, // Shopify's maximum limit per request
          query: finalQuery, // Search by name and/or category
        },
      },
    );
    const responseJson = await response.json();
    return {
      action: "getProducts",
      products: responseJson.data.products,
      searchQuery: searchQuery,
      categoryQuery: categoryQuery,
    };
  } else {
    // Create new product (existing functionality)
    const color = ["Red", "Orange", "Yellow", "Green"][
      Math.floor(Math.random() * 4)
    ];
    const response = await admin.graphql(
      `#graphql
        mutation populateProduct($product: ProductCreateInput!) {
          productCreate(product: $product) {
            product {
              id
              title
              handle
              status
              variants(first: 10) {
                edges {
                  node {
                    id
                    price
                    barcode
                    createdAt
                  }
                }
              }
            }
          }
        }`,
      {
        variables: {
          product: {
            title: `${color} Snowboard`,
          },
        },
      },
    );
    const responseJson = await response.json();
    const product = responseJson.data.productCreate.product;
    const variantId = product.variants.edges[0].node.id;
    const variantResponse = await admin.graphql(
      `#graphql
      mutation shopifyRemixTemplateUpdateVariant($productId: ID!, $variants: [ProductVariantsBulkInput!]!) {
        productVariantsBulkUpdate(productId: $productId, variants: $variants) {
          productVariants {
            id
            price
            barcode
            createdAt
          }
        }
      }`,
      {
        variables: {
          productId: product.id,
          variants: [{ id: variantId, price: "100.00" }],
        },
      },
    );
    const variantResponseJson = await variantResponse.json();

    return {
      action: "createProduct",
      product: responseJson.data.productCreate.product,
      variant: variantResponseJson.data.productVariantsBulkUpdate.productVariants,
    };
  }
};

export default function Index() {
  const fetcher = useFetcher();
  const shopify = useAppBridge();
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryQuery, setCategoryQuery] = useState("");
  const isLoading =
    ["loading", "submitting"].includes(fetcher.state) &&
    fetcher.formMethod === "POST";
  const productId = fetcher.data?.product?.id.replace(
    "gid://shopify/Product/",
    "",
  );

  useEffect(() => {
    if (productId) {
      shopify.toast.show("Product created");
    }
  }, [productId, shopify]);

  const generateProduct = () => fetcher.submit({}, { method: "POST" });
  const getProducts = () => fetcher.submit({ action: "getProducts" }, { method: "POST" });
  const searchProducts = () => fetcher.submit({
    action: "getProducts",
    searchQuery: searchQuery
  }, { method: "POST" });
  const searchByCategory = () => fetcher.submit({
    action: "getProducts",
    categoryQuery: categoryQuery
  }, { method: "POST" });
  const searchBoth = () => fetcher.submit({
    action: "getProducts",
    searchQuery: searchQuery,
    categoryQuery: categoryQuery
  }, { method: "POST" });

  return (
    <Page>
      <TitleBar title="Remix app template">
        <button variant="primary" onClick={generateProduct}>
          Generate a product
        </button>
      </TitleBar>
      <BlockStack gap="500">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="500">
                <BlockStack gap="200">
                  <Text as="h2" variant="headingMd">
                    Congrats on creating a new Shopify app 🎉
                  </Text>
                  <Text variant="bodyMd" as="p">
                    This embedded app template uses{" "}
                    <Link
                      url="https://shopify.dev/docs/apps/tools/app-bridge"
                      target="_blank"
                      removeUnderline
                    >
                      App Bridge
                    </Link>{" "}
                    interface examples like an{" "}
                    <Link url="/app/additional" removeUnderline>
                      additional page in the app nav
                    </Link>
                    , as well as an{" "}
                    <Link
                      url="https://shopify.dev/docs/api/admin-graphql"
                      target="_blank"
                      removeUnderline
                    >
                      Admin GraphQL
                    </Link>{" "}
                    mutation demo, to provide a starting point for app
                    development.
                  </Text>
                </BlockStack>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Get started with products
                  </Text>
                  <Text as="p" variant="bodyMd">
                    Generate a product with GraphQL and get the JSON output for
                    that product. Learn more about the{" "}
                    <Link
                      url="https://shopify.dev/docs/api/admin-graphql/latest/mutations/productCreate"
                      target="_blank"
                      removeUnderline
                    >
                      productCreate
                    </Link>{" "}
                    mutation in our API references.
                  </Text>
                </BlockStack>
                <InlineStack gap="300">
                  <Button loading={isLoading} onClick={generateProduct}>
                    Generate a product
                  </Button>
                  <Button loading={isLoading} onClick={getProducts} variant="secondary">
                    Get All Products
                  </Button>
                  {fetcher.data?.product && (
                    <Button
                      url={`shopify:admin/products/${productId}`}
                      target="_blank"
                      variant="plain"
                    >
                      View product
                    </Button>
                  )}
                </InlineStack>

                <BlockStack gap="400">
                  <Text as="h3" variant="headingMd">
                    Search Products
                  </Text>

                  {/* Search by Name */}
                  <BlockStack gap="200">
                    <Text as="h4" variant="headingSm">
                      Search by Product Name
                    </Text>
                    <InlineStack gap="300">
                      <div style={{ flexGrow: 1 }}>
                        <TextField
                          value={searchQuery}
                          onChange={setSearchQuery}
                          placeholder="Enter product name to search..."
                          autoComplete="off"
                        />
                      </div>
                      <Button
                        loading={isLoading}
                        onClick={searchProducts}
                        disabled={!searchQuery.trim()}
                      >
                        Search by Name
                      </Button>
                    </InlineStack>
                  </BlockStack>

                  {/* Search by Category */}
                  <BlockStack gap="200">
                    <Text as="h4" variant="headingSm">
                      Search by Category/Type
                    </Text>
                    <InlineStack gap="300">
                      <div style={{ flexGrow: 1 }}>
                        <TextField
                          value={categoryQuery}
                          onChange={setCategoryQuery}
                          placeholder="Enter product category or type..."
                          autoComplete="off"
                        />
                      </div>
                      <Button
                        loading={isLoading}
                        onClick={searchByCategory}
                        disabled={!categoryQuery.trim()}
                        variant="secondary"
                      >
                        Search by Category
                      </Button>
                    </InlineStack>
                  </BlockStack>

                  {/* Combined Search */}
                  {(searchQuery.trim() && categoryQuery.trim()) && (
                    <BlockStack gap="200">
                      <Text as="h4" variant="headingSm">
                        Combined Search
                      </Text>
                      <InlineStack gap="300">
                        <Text variant="bodyMd" color="subdued" style={{ flexGrow: 1 }}>
                          Search for "{searchQuery}" in category "{categoryQuery}"
                        </Text>
                        <Button
                          loading={isLoading}
                          onClick={searchBoth}
                          variant="primary"
                        >
                          Search Both
                        </Button>
                      </InlineStack>
                    </BlockStack>
                  )}
                </BlockStack>
                {/* Display created product */}
                {fetcher.data?.action === "createProduct" && fetcher.data?.product && (
                  <>
                    <Text as="h3" variant="headingMd">
                      {" "}
                      productCreate mutation
                    </Text>
                    <Box
                      padding="400"
                      background="bg-surface-active"
                      borderWidth="025"
                      borderRadius="200"
                      borderColor="border"
                      overflowX="scroll"
                    >
                      <pre style={{ margin: 0 }}>
                        <code>
                          {JSON.stringify(fetcher.data.product, null, 2)}
                        </code>
                      </pre>
                    </Box>
                    <Text as="h3" variant="headingMd">
                      {" "}
                      productVariantsBulkUpdate mutation
                    </Text>
                    <Box
                      padding="400"
                      background="bg-surface-active"
                      borderWidth="025"
                      borderRadius="200"
                      borderColor="border"
                      overflowX="scroll"
                    >
                      <pre style={{ margin: 0 }}>
                        <code>
                          {JSON.stringify(fetcher.data.variant, null, 2)}
                        </code>
                      </pre>
                    </Box>
                  </>
                )}

                {/* Display fetched products */}
                {fetcher.data?.action === "getProducts" && fetcher.data?.products && (
                  <Card>
                    <BlockStack gap="400">
                      <Text as="h3" variant="headingMd">
                        {(() => {
                          const { searchQuery, categoryQuery } = fetcher.data;
                          const count = fetcher.data.products.edges.length;

                          if (searchQuery && categoryQuery) {
                            return `Search Results for "${searchQuery}" in category "${categoryQuery}" (${count} found)`;
                          } else if (searchQuery) {
                            return `Search Results for "${searchQuery}" (${count} found)`;
                          } else if (categoryQuery) {
                            return `Products in category "${categoryQuery}" (${count} found)`;
                          } else {
                            return `Your Store Products (${count} found)`;
                          }
                        })()}
                      </Text>
                      {fetcher.data.products.edges.length > 0 ? (
                        <BlockStack gap="300">
                          {fetcher.data.products.edges.map(({ node: product }) => (
                            <Box
                              key={product.id}
                              padding="300"
                              background="bg-surface-secondary"
                              borderWidth="025"
                              borderRadius="200"
                              borderColor="border"
                            >
                              <BlockStack gap="200">
                                <InlineStack align="space-between">
                                  <InlineStack gap="300">
                                    {product.images.edges.length > 0 && (
                                      <img
                                        src={product.images.edges[0].node.url}
                                        alt={product.images.edges[0].node.altText || product.title}
                                        style={{
                                          width: '60px',
                                          height: '60px',
                                          objectFit: 'cover',
                                          borderRadius: '8px'
                                        }}
                                      />
                                    )}
                                    <BlockStack gap="100">
                                      <Text as="h4" variant="headingSm">
                                        {product.title}
                                      </Text>
                                      <Text variant="bodySm" color="subdued">
                                        Handle: {product.handle}
                                      </Text>
                                    </BlockStack>
                                  </InlineStack>
                                  <Text variant="bodySm" color="subdued">
                                    {product.status}
                                  </Text>
                                </InlineStack>
                                <InlineStack gap="400">
                                  <Text variant="bodyMd">
                                    <strong>Vendor:</strong> {product.vendor || 'N/A'}
                                  </Text>
                                  <Text variant="bodyMd">
                                    <strong>Type:</strong> {product.productType || 'N/A'}
                                  </Text>
                                </InlineStack>
                                <Text variant="bodyMd">
                                  <strong>Price Range:</strong> {product.priceRangeV2.minVariantPrice.amount} - {product.priceRangeV2.maxVariantPrice.amount} {product.priceRangeV2.minVariantPrice.currencyCode}
                                </Text>
                                <Text variant="bodyMd">
                                  <strong>Total Inventory:</strong> {product.totalInventory || 0}
                                </Text>
                                <Text variant="bodySm" color="subdued">
                                  Created: {new Date(product.createdAt).toLocaleDateString()}
                                </Text>
                              </BlockStack>
                            </Box>
                          ))}
                        </BlockStack>
                      ) : (
                        <Text variant="bodyMd" color="subdued">
                          No products found in your store.
                        </Text>
                      )}
                    </BlockStack>
                  </Card>
                )}

                {/* Fallback for old product creation (backward compatibility) */}
                {fetcher.data?.product && !fetcher.data?.action && (
                  <>
                    <Text as="h3" variant="headingMd">
                      {" "}
                      productCreate mutation
                    </Text>
                    <Box
                      padding="400"
                      background="bg-surface-active"
                      borderWidth="025"
                      borderRadius="200"
                      borderColor="border"
                      overflowX="scroll"
                    >
                      <pre style={{ margin: 0 }}>
                        <code>
                          {JSON.stringify(fetcher.data.product, null, 2)}
                        </code>
                      </pre>
                    </Box>
                    <Text as="h3" variant="headingMd">
                      {" "}
                      productVariantsBulkUpdate mutation
                    </Text>
                    <Box
                      padding="400"
                      background="bg-surface-active"
                      borderWidth="025"
                      borderRadius="200"
                      borderColor="border"
                      overflowX="scroll"
                    >
                      <pre style={{ margin: 0 }}>
                        <code>
                          {JSON.stringify(fetcher.data.variant, null, 2)}
                        </code>
                      </pre>
                    </Box>
                  </>
                )}
              </BlockStack>
            </Card>
          </Layout.Section>
          <Layout.Section variant="oneThird">
            <BlockStack gap="500">
              <Card>
                <BlockStack gap="200">
                  <Text as="h2" variant="headingMd">
                    App template specs
                  </Text>
                  <BlockStack gap="200">
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        Framework
                      </Text>
                      <Link
                        url="https://remix.run"
                        target="_blank"
                        removeUnderline
                      >
                        Remix
                      </Link>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        Database
                      </Text>
                      <Link
                        url="https://www.prisma.io/"
                        target="_blank"
                        removeUnderline
                      >
                        Prisma
                      </Link>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        Interface
                      </Text>
                      <span>
                        <Link
                          url="https://polaris.shopify.com"
                          target="_blank"
                          removeUnderline
                        >
                          Polaris
                        </Link>
                        {", "}
                        <Link
                          url="https://shopify.dev/docs/apps/tools/app-bridge"
                          target="_blank"
                          removeUnderline
                        >
                          App Bridge
                        </Link>
                      </span>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        API
                      </Text>
                      <Link
                        url="https://shopify.dev/docs/api/admin-graphql"
                        target="_blank"
                        removeUnderline
                      >
                        GraphQL API
                      </Link>
                    </InlineStack>
                  </BlockStack>
                </BlockStack>
              </Card>
              <Card>
                <BlockStack gap="200">
                  <Text as="h2" variant="headingMd">
                    Next steps
                  </Text>
                  <List>
                    <List.Item>
                      Build an{" "}
                      <Link
                        url="https://shopify.dev/docs/apps/getting-started/build-app-example"
                        target="_blank"
                        removeUnderline
                      >
                        {" "}
                        example app
                      </Link>{" "}
                      to get started
                    </List.Item>
                    <List.Item>
                      Explore Shopify’s API with{" "}
                      <Link
                        url="https://shopify.dev/docs/apps/tools/graphiql-admin-api"
                        target="_blank"
                        removeUnderline
                      >
                        GraphiQL
                      </Link>
                    </List.Item>
                  </List>
                </BlockStack>
              </Card>
            </BlockStack>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  );
}
