/**
 * Shopify Chatbot Widget
 * Handles the frontend functionality for the chatbot widget
 */

(function() {
  'use strict';

  class ShopifyChatbot {
    constructor() {
      this.isOpen = false;
      this.sessionId = null;
      this.apiEndpoint = '/api/chatbot';
      this.currentPage = window.location.pathname;
      this.productData = this.extractProductData();

      this.init();
    }

    init() {
      this.bindEvents();
      this.loadChatHistory();
    }

    bindEvents() {
      const toggle = document.getElementById('chatbot-toggle');
      const close = document.getElementById('chatbot-close');
      const input = document.getElementById('chatbot-input');
      const send = document.getElementById('chatbot-send');

      if (toggle) {
        toggle.addEventListener('click', () => this.toggleChat());
      }

      if (close) {
        close.addEventListener('click', () => this.closeChat());
      }

      if (input) {
        input.addEventListener('input', (e) => this.handleInputChange(e));
        input.addEventListener('keypress', (e) => {
          if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            this.sendMessage();
          }
        });
      }

      if (send) {
        send.addEventListener('click', () => this.sendMessage());
      }

      // Close chat when clicking outside
      document.addEventListener('click', (e) => {
        const widget = document.getElementById('shopify-chatbot-widget');
        if (widget && !widget.contains(e.target) && this.isOpen) {
          this.closeChat();
        }
      });
    }

    extractProductData() {
      // Try to extract product information from the current page
      const productData = {};

      // Check if we're on a product page
      if (window.location.pathname.includes('/products/')) {
        // Try multiple methods to get product data

        // Method 1: Look for product JSON in script tags
        const productJsonScript = document.querySelector('script[data-product-json]');
        if (productJsonScript) {
          try {
            productData.product = JSON.parse(productJsonScript.textContent);
          } catch (e) {
            console.warn('Could not parse product data from script tag');
          }
        }

        // Method 2: Look for Shopify product object
        if (window.ShopifyAnalytics?.meta?.product) {
          productData.product = window.ShopifyAnalytics.meta.product;
        }

        // Method 3: Extract from page elements
        const title = document.querySelector('h1, .product-title, [data-product-title]')?.textContent?.trim();
        const price = document.querySelector('.price, [data-price], .product-price')?.textContent?.trim();
        const description = document.querySelector('.product-description, [data-product-description]')?.textContent?.trim();
        const vendor = document.querySelector('.product-vendor, [data-product-vendor]')?.textContent?.trim();

        if (title) productData.title = title;
        if (price) productData.price = price;
        if (description) productData.description = description.substring(0, 200) + '...'; // Limit description length
        if (vendor) productData.vendor = vendor;

        // Extract variant information
        const variantSelect = document.querySelector('select[name="id"], input[name="id"]:checked');
        if (variantSelect) {
          productData.selectedVariant = variantSelect.value;
        }

        // Extract availability
        const addToCartButton = document.querySelector('[name="add"], .btn-product-add, .product-form__cart-submit');
        if (addToCartButton) {
          productData.available = !addToCartButton.disabled && !addToCartButton.classList.contains('disabled');
        }
      }

      // Check if we're on a collection page
      if (window.location.pathname.includes('/collections/')) {
        const collectionTitle = document.querySelector('h1, .collection-title')?.textContent?.trim();
        if (collectionTitle) {
          productData.collection = collectionTitle;
        }
      }

      return productData;
    }

    toggleChat() {
      if (this.isOpen) {
        this.closeChat();
      } else {
        this.openChat();
      }
    }

    openChat() {
      const container = document.getElementById('chatbot-container');
      if (container) {
        container.style.display = 'flex';
        this.isOpen = true;
        
        // Focus on input
        const input = document.getElementById('chatbot-input');
        if (input) {
          setTimeout(() => input.focus(), 100);
        }
        
        // Hide notification
        this.hideNotification();
      }
    }

    closeChat() {
      const container = document.getElementById('chatbot-container');
      if (container) {
        container.style.display = 'none';
        this.isOpen = false;
      }
    }

    handleInputChange(e) {
      const send = document.getElementById('chatbot-send');
      if (send) {
        send.disabled = !e.target.value.trim();
      }
    }

    async sendMessage() {
      const input = document.getElementById('chatbot-input');
      const message = input?.value?.trim();
      
      if (!message) return;

      // Add user message to chat
      this.addMessage(message, 'user');
      
      // Clear input
      input.value = '';
      this.handleInputChange({ target: { value: '' } });
      
      // Show typing indicator
      this.showTyping();
      
      try {
        const response = await this.callChatbotAPI(message);
        this.hideTyping();
        
        if (response.error) {
          this.addMessage(response.error, 'error');
        } else {
          this.addMessage(response.response, 'bot');
          if (response.sessionId) {
            this.sessionId = response.sessionId;
          }
        }
      } catch (error) {
        this.hideTyping();
        this.addMessage('Sorry, I encountered an error. Please try again.', 'error');
        console.error('Chatbot API error:', error);
      }
    }

    async callChatbotAPI(message) {
      const payload = {
        message: message,
        sessionId: this.sessionId,
        context: {
          page: this.currentPage,
          product: this.productData,
          shop: window.Shopify?.shop || null
        }
      };

      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    }

    addMessage(content, type) {
      const messagesContainer = document.getElementById('chatbot-messages');
      if (!messagesContainer) return;

      const messageDiv = document.createElement('div');
      messageDiv.className = `chatbot-message ${type}-message`;
      
      const contentDiv = document.createElement('div');
      contentDiv.className = 'message-content';
      contentDiv.textContent = content;
      
      messageDiv.appendChild(contentDiv);
      messagesContainer.appendChild(messageDiv);
      
      // Scroll to bottom
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    showTyping() {
      const typing = document.getElementById('chatbot-typing');
      if (typing) {
        typing.style.display = 'flex';
      }
    }

    hideTyping() {
      const typing = document.getElementById('chatbot-typing');
      if (typing) {
        typing.style.display = 'none';
      }
    }

    showNotification(count = '') {
      const notification = document.getElementById('chatbot-notification');
      if (notification) {
        notification.textContent = count;
        notification.style.display = 'flex';
      }
    }

    hideNotification() {
      const notification = document.getElementById('chatbot-notification');
      if (notification) {
        notification.style.display = 'none';
      }
    }

    loadChatHistory() {
      // Load chat history from localStorage if available
      const savedSessionId = localStorage.getItem('shopify-chatbot-session');
      if (savedSessionId) {
        this.sessionId = savedSessionId;
      }
    }

    saveChatHistory() {
      // Save session ID to localStorage
      if (this.sessionId) {
        localStorage.setItem('shopify-chatbot-session', this.sessionId);
      }
    }
  }

  // Initialize chatbot when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      window.ShopifyChatbot.instance = new ShopifyChatbot();
    });
  } else {
    window.ShopifyChatbot.instance = new ShopifyChatbot();
  }

  // Expose chatbot class globally
  window.ShopifyChatbot.ChatbotWidget = ShopifyChatbot;

})();
