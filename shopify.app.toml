# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "4e6034e463f071a3292f1fdfde3e017d"
name = "internation-chatbot"
handle = "internation-chatbot"
application_url = "https://blade-combinations-stylus-killer.trycloudflare.com"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-07"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_products"

[auth]
redirect_urls = ["https://blade-combinations-stylus-killer.trycloudflare.com/auth/callback", "https://blade-combinations-stylus-killer.trycloudflare.com/auth/shopify/callback", "https://blade-combinations-stylus-killer.trycloudflare.com/api/auth/callback"]

[pos]
embedded = false
