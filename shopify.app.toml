# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "4e6034e463f071a3292f1fdfde3e017d"
name = "waitlist-manager"
handle = "waitlist-manager"
application_url = "https://ignored-lamb-anthropology-grateful.trycloudflare.com"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-07"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_products"

[auth]
redirect_urls = ["https://ignored-lamb-anthropology-grateful.trycloudflare.com/auth/callback", "https://ignored-lamb-anthropology-grateful.trycloudflare.com/auth/shopify/callback", "https://ignored-lamb-anthropology-grateful.trycloudflare.com/api/auth/callback"]

[pos]
embedded = false

[app_proxy]
url= "https://ignored-lamb-anthropology-grateful.trycloudflare.com/apps/api/public/waitlist"
subpath= "wait-wise"
prefix = "apps"

