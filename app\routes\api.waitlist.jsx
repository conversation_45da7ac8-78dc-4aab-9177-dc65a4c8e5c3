import { json } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { createWaitlistService } from "../services/waitlist.server";

export const action = async ({ request }) => {
  const { session } = await authenticate.admin(request);

  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    const body = await request.json();
    const { action, data } = body;

    // Create waitlist service instance
    const waitlistService = createWaitlistService(session.shop);

    switch (action) {
      case "bulk_create":
        return await handleBulkCreate(waitlistService, data);

      case "bulk_update_priority":
        return await handleBulkUpdatePriority(waitlistService, data);

      case "bulk_delete":
        return await handleBulkDelete(waitlistService, data);

      case "export_waitlist":
        return await handleExportWaitlist(waitlistService, data);

      case "get_stats":
        return await handleGetStats(waitlistService);

      default:
        return Response.json({ error: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    console.error("Waitlist API error:", error);
    return Response.json({
      error: "Internal server error",
      message: error.message
    }, { status: 500 });
  }
};

// Bulk create waitlist entries
async function handleBulkCreate(waitlistService, data) {
  const { emails, productVariantId, priority = "Medium" } = data;

  if (!emails || !Array.isArray(emails) || !productVariantId) {
    return Response.json({ error: "Invalid data format" }, { status: 400 });
  }

  try {
    const result = await waitlistService.bulkCreateEntries(emails, productVariantId, priority);

    return Response.json({
      success: true,
      message: `Successfully added ${result.created} entries to waitlist`,
      created: result.created,
      duplicates: result.duplicates,
      invalid: result.invalid,
    });

  } catch (error) {
    console.error("Bulk create error:", error);
    return Response.json({ error: error.message || "Failed to create waitlist entries" }, { status: 500 });
  }
}

// Bulk update priority
async function handleBulkUpdatePriority(waitlistService, data) {
  const { waitlistIds, priority } = data;

  if (!waitlistIds || !Array.isArray(waitlistIds) || !priority) {
    return Response.json({ error: "Invalid data format" }, { status: 400 });
  }

  try {
    const result = await waitlistService.bulkUpdatePriority(waitlistIds, priority);

    return Response.json({
      success: true,
      message: `Updated priority for ${result.count} waitlist entries`,
      updated: result.count,
    });

  } catch (error) {
    console.error("Bulk update error:", error);
    return Response.json({ error: "Failed to update waitlist entries" }, { status: 500 });
  }
}

// Bulk delete
async function handleBulkDelete(waitlistService, data) {
  const { waitlistIds } = data;

  if (!waitlistIds || !Array.isArray(waitlistIds)) {
    return Response.json({ error: "Invalid data format" }, { status: 400 });
  }

  try {
    const result = await waitlistService.bulkDeleteEntries(waitlistIds);

    return Response.json({
      success: true,
      message: `Deleted ${result.count} waitlist entries`,
      deleted: result.count,
    });

  } catch (error) {
    console.error("Bulk delete error:", error);
    return Response.json({ error: "Failed to delete waitlist entries" }, { status: 500 });
  }
}

// Export waitlist data
async function handleExportWaitlist(waitlistService, data) {
  const { productVariantId, priority } = data || {};

  try {
    const exportData = await waitlistService.exportWaitlist({ productVariantId, priority });

    return Response.json({
      success: true,
      data: exportData,
      count: exportData.length,
    });

  } catch (error) {
    console.error("Export error:", error);
    return Response.json({ error: "Failed to export waitlist data" }, { status: 500 });
  }
}

// Get waitlist statistics
async function handleGetStats(waitlistService) {
  try {
    const stats = await waitlistService.getWaitlistStats();

    return Response.json({
      success: true,
      stats,
    });

  } catch (error) {
    console.error("Stats error:", error);
    return Response.json({ error: "Failed to get waitlist statistics" }, { status: 500 });
  }
}
