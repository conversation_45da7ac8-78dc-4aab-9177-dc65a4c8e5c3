import { useState, useCallback } from "react";
import { json } from "@remix-run/node";
import { useActionData, useLoaderData, useSubmit, useNavigation } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  TextField,
  Button,
  BlockStack,
  Text,
  Banner,
  ColorPicker,
  Checkbox,
  Select,
  FormLayout
} from "@shopify/polaris";
import { authenticate } from "../../shopify.server";
import prisma from "../../db.server";
import { syncChatbotSettingsToMetafields } from "../../services/metafields";

export const loader = async ({ request }) => {
  const { session } = await authenticate.admin(request);
  
  // Get existing chatbot settings for this shop
  let settings = await prisma.chatbotSettings.findUnique({
    where: { shopId: session.shop }
  });
  
  // Create default settings if none exist
  if (!settings) {
    settings = await prisma.chatbotSettings.create({
      data: {
        shopId: session.shop,
        enabled: true,
        widgetTitle: "Chat with us",
        welcomeMessage: "Hello! How can I help you today?",
        inputPlaceholder: "Type your message...",
        primaryColor: "#007cba",
        position: "bottom-right",
        showOnPages: ["all"]
      }
    });
  }
  
  return json({
    settings,
    isGroqConfigured: Boolean(process.env.GROQ_API_KEY)
  });
};

export const action = async ({ request }) => {
  const { session } = await authenticate.admin(request);
  const formData = await request.formData();
  
  const settings = {
    enabled: formData.get("enabled") === "true",
    widgetTitle: formData.get("widgetTitle") || "Chat with us",
    welcomeMessage: formData.get("welcomeMessage") || "Hello! How can I help you today?",
    inputPlaceholder: formData.get("inputPlaceholder") || "Type your message...",
    primaryColor: formData.get("primaryColor") || "#007cba",
    position: formData.get("position") || "bottom-right",
    showOnPages: JSON.parse(formData.get("showOnPages") || '["all"]')
  };
  
  try {
    // Save to database
    await prisma.chatbotSettings.upsert({
      where: { shopId: session.shop },
      update: settings,
      create: {
        shopId: session.shop,
        ...settings
      }
    });

    // Sync to Shopify metafields for theme access
    const metafieldSuccess = await syncChatbotSettingsToMetafields(session.shop, settings);

    if (!metafieldSuccess) {
      console.warn("Failed to sync settings to metafields, but database save was successful");
    }

    return json({
      success: true,
      message: "Settings saved successfully!" + (metafieldSuccess ? "" : " (Theme integration may need manual setup)")
    });
  } catch (error) {
    console.error("Error saving chatbot settings:", error);
    return json({ success: false, error: "Failed to save settings" });
  }
};

export default function ChatbotSettings() {
  const { settings, isGroqConfigured } = useLoaderData();
  const actionData = useActionData();
  const navigation = useNavigation();
  const submit = useSubmit();
  
  const [formData, setFormData] = useState({
    enabled: settings.enabled,
    widgetTitle: settings.widgetTitle,
    welcomeMessage: settings.welcomeMessage,
    inputPlaceholder: settings.inputPlaceholder,
    primaryColor: settings.primaryColor,
    position: settings.position,
    showOnPages: settings.showOnPages
  });
  
  const isSubmitting = navigation.state === "submitting";
  
  const handleSubmit = useCallback(() => {
    const data = new FormData();
    Object.entries(formData).forEach(([key, value]) => {
      if (key === "showOnPages") {
        data.append(key, JSON.stringify(value));
      } else {
        data.append(key, value.toString());
      }
    });
    
    submit(data, { method: "post" });
  }, [formData, submit]);
  
  const positionOptions = [
    { label: "Bottom Right", value: "bottom-right" },
    { label: "Bottom Left", value: "bottom-left" },
    { label: "Top Right", value: "top-right" },
    { label: "Top Left", value: "top-left" }
  ];
  
  const pageOptions = [
    { label: "All Pages", value: "all" },
    { label: "Product Pages Only", value: "products" },
    { label: "Collection Pages Only", value: "collections" },
    { label: "Home Page Only", value: "home" }
  ];
  
  if (!isGroqConfigured) {
    return (
      <Page title="Chatbot Settings">
        <Layout>
          <Layout.Section>
            <Banner
              title="Configuration Required"
              status="critical"
            >
              <p>Groq API key is not configured. Please add GROQ_API_KEY to your environment variables to enable the chatbot.</p>
            </Banner>
          </Layout.Section>
        </Layout>
      </Page>
    );
  }
  
  return (
    <Page
      title="Chatbot Settings"
      primaryAction={{
        content: "Save Settings",
        onAction: handleSubmit,
        loading: isSubmitting
      }}
    >
      <Layout>
        <Layout.Section>
          {actionData?.success && (
            <Banner status="success" title="Success">
              <p>{actionData.message}</p>
            </Banner>
          )}
          
          {actionData?.error && (
            <Banner status="critical" title="Error">
              <p>{actionData.error}</p>
            </Banner>
          )}
          
          <Card>
            <Card.Section>
              <BlockStack gap="4">
                <Text as="h2" variant="headingMd">
                  General Settings
                </Text>
                
                <Checkbox
                  label="Enable Chatbot Widget"
                  checked={formData.enabled}
                  onChange={(checked) => setFormData(prev => ({ ...prev, enabled: checked }))}
                  helpText="Show the chatbot widget on your storefront"
                />
                
                <FormLayout>
                  <TextField
                    label="Widget Title"
                    value={formData.widgetTitle}
                    onChange={(value) => setFormData(prev => ({ ...prev, widgetTitle: value }))}
                    helpText="Title displayed in the chatbot header"
                  />
                  
                  <TextField
                    label="Welcome Message"
                    value={formData.welcomeMessage}
                    onChange={(value) => setFormData(prev => ({ ...prev, welcomeMessage: value }))}
                    multiline={3}
                    helpText="First message shown to customers"
                  />
                  
                  <TextField
                    label="Input Placeholder"
                    value={formData.inputPlaceholder}
                    onChange={(value) => setFormData(prev => ({ ...prev, inputPlaceholder: value }))}
                    helpText="Placeholder text in the message input field"
                  />
                </FormLayout>
              </BlockStack>
            </Card.Section>
          </Card>

          <Card>
            <Card.Section>
              <BlockStack gap="4">
                <Text as="h2" variant="headingMd">
                  Appearance
                </Text>

                <FormLayout>
                  <div>
                    <Text as="p" variant="bodyMd">Primary Color</Text>
                    <div style={{ marginTop: "8px" }}>
                      <ColorPicker
                        color={formData.primaryColor}
                        onChange={(color) => setFormData(prev => ({ ...prev, primaryColor: color }))}
                      />
                    </div>
                    <Text as="p" variant="bodySm" color="subdued">
                      Color used for the widget button and message bubbles
                    </Text>
                  </div>

                  <Select
                    label="Widget Position"
                    options={positionOptions}
                    value={formData.position}
                    onChange={(value) => setFormData(prev => ({ ...prev, position: value }))}
                    helpText="Where to display the chatbot widget on the page"
                  />
                </FormLayout>
              </BlockStack>
            </Card.Section>
          </Card>

          <Card>
            <Card.Section>
              <BlockStack gap="4">
                <Text as="h2" variant="headingMd">
                  Display Options
                </Text>

                <Select
                  label="Show Widget On"
                  options={pageOptions}
                  value={formData.showOnPages[0] || "all"}
                  onChange={(value) => setFormData(prev => ({ ...prev, showOnPages: [value] }))}
                  helpText="Choose which pages should display the chatbot widget"
                />
              </BlockStack>
            </Card.Section>
          </Card>
        </Layout.Section>
        
        <Layout.Section secondary>
          <Card>
            <Card.Section>
              <BlockStack gap="4">
                <Text as="h2" variant="headingMd">
                  Preview
                </Text>

                <div style={{
                  position: "relative",
                  height: "200px",
                  background: "#f6f6f7",
                  borderRadius: "8px",
                  overflow: "hidden"
                }}>
                  <div style={{
                    position: "absolute",
                    bottom: "20px",
                    right: formData.position.includes("right") ? "20px" : "auto",
                    left: formData.position.includes("left") ? "20px" : "auto",
                    top: formData.position.includes("top") ? "20px" : "auto",
                    width: "50px",
                    height: "50px",
                    background: formData.primaryColor,
                    borderRadius: "50%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    color: "white",
                    fontSize: "20px"
                  }}>
                    💬
                  </div>
                </div>

                <Text as="p" variant="bodySm" color="subdued">
                  This is how your chatbot widget will appear on your storefront
                </Text>
              </BlockStack>
            </Card.Section>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
