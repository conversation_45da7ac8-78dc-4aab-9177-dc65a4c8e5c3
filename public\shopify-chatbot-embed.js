/**
 * Shopify AI Chatbot - Easy Embed Script
 * Add this script to your Shopify theme to enable the AI chatbot
 * 
 * Usage:
 * <script src="YOUR_APP_URL/shopify-chatbot-embed.js"></script>
 * <script>
 *   ShopifyChatbot.init({
 *     apiEndpoint: 'YOUR_APP_URL/api/chatbot',
 *     shopDomain: 'your-shop.myshopify.com',
 *     title: 'Chat with us',
 *     welcomeMessage: 'Hello! How can I help you today?',
 *     primaryColor: '#667eea'
 *   });
 * </script>
 */

(function() {
    'use strict';

    // Default configuration
    const DEFAULT_CONFIG = {
        apiEndpoint: '/api/chatbot',
        shopDomain: window.location.hostname,
        title: 'Chat with us',
        welcomeMessage: 'Hello! How can I help you today?',
        primaryColor: '#667eea',
        position: 'bottom-right',
        autoOpen: false,
        showNotification: true,
        enabled: true
    };

    // Chatbot HTML template
    const CHATBOT_HTML = `
        <div id="shopify-chatbot-widget" class="shopify-chatbot-widget" style="display: none;">
            <button class="chatbot-toggle" id="chatbot-toggle" aria-label="Open chat">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 2H4C2.9 2 2 2.9 2 4V22L6 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2ZM20 16H5.17L4 17.17V4H20V16Z" fill="currentColor"/>
                    <path d="M7 9H17V11H7V9ZM7 12H15V14H7V12Z" fill="currentColor"/>
                </svg>
                <span class="chatbot-notification" id="chatbot-notification">💬</span>
            </button>
            
            <div class="chatbot-container" id="chatbot-container">
                <div class="chatbot-header">
                    <h3 id="chatbot-title">Chat with us</h3>
                    <button class="chatbot-close" id="chatbot-close" aria-label="Close chat">&times;</button>
                </div>
                
                <div class="chatbot-messages" id="chatbot-messages">
                    <div class="welcome-message">
                        <span class="icon">🤖</span>
                        <div>
                            <strong>Hello! I'm your AI assistant</strong><br>
                            <span id="welcome-text">How can I help you today?</span>
                        </div>
                    </div>
                </div>
                
                <div class="chatbot-typing" id="chatbot-typing">
                    <div class="typing-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <span>AI is thinking...</span>
                </div>
                
                <div class="chatbot-input-container">
                    <textarea 
                        id="chatbot-input" 
                        class="chatbot-input"
                        placeholder="Type your message..." 
                        rows="1"
                        maxlength="500"
                    ></textarea>
                    <button id="chatbot-send" class="chatbot-send" disabled aria-label="Send message">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M2.01 21L23 12L2.01 3L2 10L17 12L2 14L2.01 21Z" fill="currentColor"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    `;

    // Chatbot CSS styles
    const CHATBOT_CSS = `
        .shopify-chatbot-widget {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 9999;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        }

        .chatbot-toggle {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            color: white;
            position: relative;
            border: none;
            outline: none;
        }

        .chatbot-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
        }

        .chatbot-toggle.active {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }

        .chatbot-notification {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: none;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .chatbot-container {
            position: absolute;
            bottom: 80px;
            right: 0;
            width: 350px;
            height: 500px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            display: none;
            flex-direction: column;
            overflow: hidden;
            animation: slideUp 0.3s ease;
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .chatbot-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chatbot-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .chatbot-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.2s ease;
        }

        .chatbot-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .chatbot-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
            background: #f8f9fa;
        }

        .chatbot-message {
            max-width: 85%;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .bot-message { align-self: flex-start; }
        .user-message { align-self: flex-end; }

        .message-content {
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.5;
            word-wrap: break-word;
        }

        .bot-message .message-content {
            background: white;
            color: #333;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .user-message .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .error-message .message-content {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 4px;
        }

        .chatbot-input-container {
            padding: 20px;
            border-top: 1px solid #e9ecef;
            background: white;
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .chatbot-input {
            flex: 1;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            padding: 12px 16px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.2s ease;
            resize: none;
            min-height: 20px;
            max-height: 100px;
            font-family: inherit;
        }

        .chatbot-input:focus {
            border-color: #667eea;
        }

        .chatbot-send {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: white;
            transition: all 0.2s ease;
        }

        .chatbot-send:disabled {
            background: #dee2e6;
            cursor: not-allowed;
        }

        .chatbot-send:not(:disabled):hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .chatbot-typing {
            padding: 12px 20px;
            display: none;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #6c757d;
            background: #f8f9fa;
        }

        .typing-indicator {
            display: flex;
            gap: 4px;
        }

        .typing-indicator span {
            width: 8px;
            height: 8px;
            background: #6c757d;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
        .typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .welcome-message {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }

        .welcome-message .icon {
            font-size: 48px;
            margin-bottom: 16px;
            display: block;
        }

        @media (max-width: 480px) {
            .shopify-chatbot-widget {
                bottom: 10px;
                right: 10px;
                left: 10px;
            }
            
            .chatbot-container {
                width: 100%;
                height: 70vh;
                bottom: 80px;
                right: 0;
                left: 0;
                border-radius: 16px 16px 0 0;
            }
        }
    `;

    // Main ShopifyChatbot object
    window.ShopifyChatbot = {
        instance: null,
        config: {},

        init: function(userConfig = {}) {
            this.config = { ...DEFAULT_CONFIG, ...userConfig };
            
            if (!this.config.enabled) {
                return;
            }

            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.create());
            } else {
                this.create();
            }
        },

        create: function() {
            // Inject CSS
            this.injectCSS();
            
            // Inject HTML
            this.injectHTML();
            
            // Load and initialize the widget
            this.loadWidget();
        },

        injectCSS: function() {
            const style = document.createElement('style');
            style.textContent = CHATBOT_CSS;
            document.head.appendChild(style);
        },

        injectHTML: function() {
            const container = document.createElement('div');
            container.innerHTML = CHATBOT_HTML;
            document.body.appendChild(container.firstElementChild);
        },

        loadWidget: function() {
            // Create a simple widget class inline
            const widget = document.getElementById('shopify-chatbot-widget');
            if (!widget) return;

            // Show the widget
            widget.style.display = 'block';

            // Initialize with basic functionality
            this.initializeBasicWidget();
        },

        initializeBasicWidget: function() {
            const toggle = document.getElementById('chatbot-toggle');
            const close = document.getElementById('chatbot-close');
            const container = document.getElementById('chatbot-container');
            const input = document.getElementById('chatbot-input');
            const send = document.getElementById('chatbot-send');
            const title = document.getElementById('chatbot-title');
            const welcomeText = document.getElementById('welcome-text');

            let isOpen = false;
            let sessionId = null;

            // Set configuration
            if (title) title.textContent = this.config.title;
            if (welcomeText) welcomeText.textContent = this.config.welcomeMessage;

            // Apply custom colors
            this.applyCustomColors();

            // Toggle chat
            if (toggle) {
                toggle.addEventListener('click', () => {
                    isOpen = !isOpen;
                    container.style.display = isOpen ? 'flex' : 'none';
                    toggle.classList.toggle('active', isOpen);
                    
                    if (isOpen) {
                        input?.focus();
                        document.getElementById('chatbot-notification').style.display = 'none';
                    }
                });
            }

            // Close chat
            if (close) {
                close.addEventListener('click', () => {
                    isOpen = false;
                    container.style.display = 'none';
                    toggle.classList.remove('active');
                });
            }

            // Handle input
            if (input) {
                input.addEventListener('input', (e) => {
                    send.disabled = !e.target.value.trim();
                    
                    // Auto-resize
                    input.style.height = 'auto';
                    input.style.height = Math.min(input.scrollHeight, 100) + 'px';
                });

                input.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });
            }

            // Send button
            if (send) {
                send.addEventListener('click', () => this.sendMessage());
            }

            // Store references
            this.elements = { toggle, close, container, input, send };
            this.state = { isOpen, sessionId };
        },

        applyCustomColors: function() {
            const style = document.createElement('style');
            const color = this.config.primaryColor;
            const darkerColor = this.adjustColor(color, -20);
            
            style.textContent = `
                .chatbot-toggle {
                    background: linear-gradient(135deg, ${color} 0%, ${darkerColor} 100%) !important;
                }
                .chatbot-header {
                    background: linear-gradient(135deg, ${color} 0%, ${darkerColor} 100%) !important;
                }
                .user-message .message-content {
                    background: linear-gradient(135deg, ${color} 0%, ${darkerColor} 100%) !important;
                }
                .chatbot-send {
                    background: linear-gradient(135deg, ${color} 0%, ${darkerColor} 100%) !important;
                }
                .chatbot-input:focus {
                    border-color: ${color} !important;
                }
            `;
            document.head.appendChild(style);
        },

        adjustColor: function(color, percent) {
            const num = parseInt(color.replace("#", ""), 16);
            const amt = Math.round(2.55 * percent);
            const R = (num >> 16) + amt;
            const G = (num >> 8 & 0x00FF) + amt;
            const B = (num & 0x0000FF) + amt;
            return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
                (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
                (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
        },

        sendMessage: async function() {
            const input = this.elements.input;
            const message = input?.value?.trim();
            
            if (!message) return;

            // Add user message
            this.addMessage(message, 'user');
            
            // Clear input
            input.value = '';
            input.style.height = 'auto';
            this.elements.send.disabled = true;
            
            // Show typing
            this.showTyping();
            
            try {
                const response = await fetch(this.config.apiEndpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        sessionId: this.state.sessionId,
                        context: {
                            url: window.location.href,
                            shop: this.config.shopDomain
                        }
                    })
                });

                const data = await response.json();
                this.hideTyping();
                
                if (data.error) {
                    this.addMessage(data.error, 'error');
                } else {
                    this.addMessage(data.response, 'bot');
                    if (data.sessionId) {
                        this.state.sessionId = data.sessionId;
                    }
                }
            } catch (error) {
                this.hideTyping();
                this.addMessage('Sorry, I encountered an error. Please try again.', 'error');
            }
        },

        addMessage: function(content, type) {
            const messagesContainer = document.getElementById('chatbot-messages');
            if (!messagesContainer) return;

            // Remove welcome message
            const welcomeMessage = messagesContainer.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.remove();
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `chatbot-message ${type}-message`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            const timeDiv = document.createElement('div');
            timeDiv.className = 'message-time';
            timeDiv.textContent = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            
            messageDiv.appendChild(contentDiv);
            messageDiv.appendChild(timeDiv);
            messagesContainer.appendChild(messageDiv);
            
            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        },

        showTyping: function() {
            const typing = document.getElementById('chatbot-typing');
            if (typing) typing.style.display = 'flex';
        },

        hideTyping: function() {
            const typing = document.getElementById('chatbot-typing');
            if (typing) typing.style.display = 'none';
        }
    };

    // Auto-initialize if config is provided
    if (window.shopifyChatbotConfig) {
        window.ShopifyChatbot.init(window.shopifyChatbotConfig);
    }

})();
