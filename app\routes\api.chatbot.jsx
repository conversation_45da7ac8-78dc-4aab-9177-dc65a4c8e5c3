import { json } from "@remix-run/node";
import { createStorefrontChatSession } from "../services/chatbot";

export const action = async ({ request }) => {
  // Set CORS headers for cross-origin requests
  const headers = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, X-Requested-With",
  };

  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405, headers });
  }

  try {
    const body = await request.json();
    const { message, sessionId, context } = body;

    if (!message || typeof message !== "string") {
      return json({ error: "Message is required" }, { status: 400, headers });
    }

    // Extract shop domain from request headers or context
    const shopDomain = context?.shop || request.headers.get("origin")?.replace(/^https?:\/\//, "");

    if (!shopDomain) {
      return json({ error: "Shop domain not found" }, { status: 400, headers });
    }

    // Check if Groq API key is configured
    if (!process.env.GROQ_API_KEY) {
      return json({
        error: "Chatbot service is temporarily unavailable. Please try again later."
      }, { status: 503, headers });
    }

    // Create or get existing chat session
    const chatSession = createStorefrontChatSession(shopDomain, sessionId);

    // Send message with context
    const response = await chatSession.sendMessage(message, context);

    return json({
      response,
      sessionId: chatSession.sessionId,
      success: true,
      timestamp: new Date().toISOString()
    }, { headers });

  } catch (error) {
    console.error("Storefront chatbot API error:", error);

    // Return user-friendly error message
    return json({
      error: "I'm having trouble processing your request right now. Please try again in a moment.",
      success: false
    }, { status: 500, headers });
  }
};

// Handle preflight requests for CORS
export const loader = async ({ request }) => {
  if (request.method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, X-Requested-With",
      },
    });
  }
  
  return json({ error: "Method not allowed" }, { status: 405 });
};
