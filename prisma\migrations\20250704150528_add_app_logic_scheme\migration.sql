-- CreateTable
CREATE TABLE "Store" (
    "shop" TEXT NOT NULL PRIMARY KEY,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "ProductVariant" (
    "id" BIGINT NOT NULL PRIMARY KEY,
    "enabled" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "inventoryLevel" INTEGER NOT NULL DEFAULT 0,
    "productId" BIGINT NOT NULL,
    "shop" TEXT NOT NULL,
    CONSTRAINT "ProductVariant_shop_fkey" FOREIGN KEY ("shop") REFERENCES "Store" ("shop") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "Waitlist" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "email" TEXT NOT NULL,
    "productVariantId" BIGINT NOT NULL,
    "priority" TEXT NOT NULL DEFAULT 'Medium',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "shop" TEXT NOT NULL,
    CONSTRAINT "Waitlist_shop_fkey" FOREIGN KEY ("shop") REFERENCES "Store" ("shop") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "Waitlist_productVariantId_fkey" FOREIGN KEY ("productVariantId") REFERENCES "ProductVariant" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
