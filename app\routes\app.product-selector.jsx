import { useState, useCallback } from "react";
import { use<PERSON><PERSON><PERSON>, useLoaderData } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Button,
  BlockStack,
  Text,
  InlineStack,
  Badge,
  Thumbnail,
  EmptyState,
  Banner,
  Spinner,

} from "@shopify/polaris";
import { TitleBar, useAppBridge } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";
import { prisma } from "../db.server";

export const loader = async ({ request }) => {
  const { session } = await authenticate.admin(request);
  
  // Get saved products from our database
  const savedProducts = await prisma.productVariant.findMany({
    where: {
      shop: session.shop,
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  return {
    shop: session.shop,
    savedProducts,
  };
};

export const action = async ({ request }) => {
  const { session } = await authenticate.admin(request);
  const formData = await request.formData();
  const actionType = formData.get("action");

  if (actionType === "save_product") {
    const productData = JSON.parse(formData.get("productData"));
    
    try {
      // Save each variant to our database
      const savedVariants = [];
      
      for (const variant of productData.variants) {
        const savedVariant = await prisma.productVariant.upsert({
          where: {
            id: BigInt(variant.id.replace('gid://shopify/ProductVariant/', '')),
          },
          update: {
            enabled: true,
            inventoryLevel: variant.inventoryQuantity || 0,
            updatedAt: new Date(),
          },
          create: {
            id: BigInt(variant.id.replace('gid://shopify/ProductVariant/', '')),
            productId: BigInt(productData.id.replace('gid://shopify/Product/', '')),
            shop: session.shop,
            enabled: true,
            inventoryLevel: variant.inventoryQuantity || 0,
          },
        });
        savedVariants.push(savedVariant);
      }

      return {
        success: true,
        message: `Product "${productData.title}" saved successfully with ${savedVariants.length} variants!`,
        savedVariants,
      };
    } catch (error) {
      console.error("Error saving product:", error);
      return {
        success: false,
        message: "Failed to save product. Please try again.",
        error: error.message,
      };
    }
  }

  if (actionType === "remove_product") {
    const productId = formData.get("productId");

    try {
      await prisma.productVariant.deleteMany({
        where: {
          productId: BigInt(productId),
          shop: session.shop,
        },
      });

      return {
        success: true,
        message: "Product removed successfully!",
      };
    } catch (error) {
      console.error("Error removing product:", error);
      return {
        success: false,
        message: "Failed to remove product. Please try again.",
      };
    }
  }

  if (actionType === "toggle_variant") {
    const variantId = formData.get("variantId");
    const enabled = formData.get("enabled") === "true";

    try {
      await prisma.productVariant.update({
        where: {
          id: BigInt(variantId),
          shop: session.shop,
        },
        data: {
          enabled: !enabled,
        },
      });

      return {
        success: true,
        message: `Variant ${!enabled ? 'enabled' : 'disabled'} successfully!`,
      };
    } catch (error) {
      console.error("Error toggling variant:", error);
      return {
        success: false,
        message: "Failed to update variant. Please try again.",
      };
    }
  }

  return { success: false, message: "Invalid action" };
};

export default function ProductSelector() {
  const { savedProducts } = useLoaderData();
  const fetcher = useFetcher();
  const shopify = useAppBridge();
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isSelecting, setIsSelecting] = useState(false);

  const isLoading = fetcher.state === "submitting";

  // Group saved products by productId
  const groupedProducts = savedProducts.reduce((acc, variant) => {
    const productId = variant.productId.toString();
    if (!acc[productId]) {
      acc[productId] = [];
    }
    acc[productId].push(variant);
    return acc;
  }, {});

  const handleProductSelect = useCallback(async () => {
    setIsSelecting(true);
    
    try {
      const selection = await shopify.resourcePicker({
        type: "product",
        action: "select",
        multiple: false,
        filter: {
          variants: true,
          draft: false,
          archived: false,
        },
      });

      if (selection && selection.length > 0) {
        const product = selection[0];
        setSelectedProduct(product);
      }
    } catch (error) {
      console.error("Product selection error:", error);
      shopify.toast.show("Failed to select product", { isError: true });
    } finally {
      setIsSelecting(false);
    }
  }, [shopify]);

  const handleSaveProduct = () => {
    if (!selectedProduct) return;

    const formData = new FormData();
    formData.append("action", "save_product");
    formData.append("productData", JSON.stringify(selectedProduct));
    
    fetcher.submit(formData, { method: "post" });
  };

  const handleRemoveProduct = (productId) => {
    const formData = new FormData();
    formData.append("action", "remove_product");
    formData.append("productId", productId);

    fetcher.submit(formData, { method: "post" });
  };

  const handleToggleVariant = (variantId, enabled) => {
    const formData = new FormData();
    formData.append("action", "toggle_variant");
    formData.append("variantId", variantId.toString());
    formData.append("enabled", enabled.toString());

    fetcher.submit(formData, { method: "post" });
  };

  // Show success/error messages
  if (fetcher.data?.message) {
    shopify.toast.show(fetcher.data.message, { 
      isError: !fetcher.data.success 
    });
  }

  return (
    <Page>
      <TitleBar title="Product Selector" />
      
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text as="h2" variant="headingMd">
                Select Product from Shopify
              </Text>
              
              <Text as="p" variant="bodyMd" color="subdued">
                Use the product picker to select a product from your Shopify store and save it to your database.
              </Text>

              <InlineStack gap="300">
                <Button
                  primary
                  onClick={handleProductSelect}
                  loading={isSelecting}
                  disabled={isLoading}
                >
                  {isSelecting ? "Selecting..." : "Select Product"}
                </Button>
                
                {selectedProduct && (
                  <Button
                    onClick={handleSaveProduct}
                    loading={isLoading}
                    disabled={!selectedProduct}
                  >
                    Save to Database
                  </Button>
                )}
              </InlineStack>

              {fetcher.data?.success === false && (
                <Banner status="critical">
                  <Text as="p">{fetcher.data.message}</Text>
                </Banner>
              )}
            </BlockStack>
          </Card>
        </Layout.Section>

        {selectedProduct && (
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <Text as="h3" variant="headingMd">
                  Selected Product
                </Text>
                
                <InlineStack gap="400" align="start">
                  <Thumbnail
                    source={selectedProduct.images?.[0]?.originalSrc || ""}
                    alt={selectedProduct.title}
                    size="large"
                  />
                  
                  <BlockStack gap="200">
                    <Text as="h4" variant="headingSm">
                      {selectedProduct.title}
                    </Text>
                    
                    <Text as="p" variant="bodyMd" color="subdued">
                      Product ID: {selectedProduct.id.replace('gid://shopify/Product/', '')}
                    </Text>
                    
                    <InlineStack gap="200">
                      <Badge status="info">
                        {selectedProduct.variants?.length || 0} variants
                      </Badge>
                      <Badge status={selectedProduct.status === 'ACTIVE' ? 'success' : 'attention'}>
                        {selectedProduct.status}
                      </Badge>
                    </InlineStack>

                    {selectedProduct.variants && selectedProduct.variants.length > 0 && (
                      <BlockStack gap="100">
                        <Text as="p" variant="bodyMd" fontWeight="medium">
                          Variants:
                        </Text>
                        {selectedProduct.variants.slice(0, 3).map((variant) => (
                          <Text key={variant.id} as="p" variant="bodySm" color="subdued">
                            • {variant.title} - ${variant.price}
                            {variant.inventoryQuantity !== null && 
                              ` (${variant.inventoryQuantity} in stock)`
                            }
                          </Text>
                        ))}
                        {selectedProduct.variants.length > 3 && (
                          <Text as="p" variant="bodySm" color="subdued">
                            ... and {selectedProduct.variants.length - 3} more variants
                          </Text>
                        )}
                      </BlockStack>
                    )}
                  </BlockStack>
                </InlineStack>
              </BlockStack>
            </Card>
          </Layout.Section>
        )}

        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text as="h3" variant="headingMd">
                Saved Products ({Object.keys(groupedProducts).length})
              </Text>
              
              {Object.keys(groupedProducts).length === 0 ? (
                <EmptyState
                  heading="No products saved yet"
                  image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
                >
                  <Text as="p">Select and save products to see them here.</Text>
                </EmptyState>
              ) : (
                <BlockStack gap="300">
                  {Object.entries(groupedProducts).map(([productId, variants]) => (
                    <Card key={productId}>
                      <BlockStack gap="300">
                        <InlineStack align="space-between">
                          <BlockStack gap="200">
                            <Text as="h4" variant="headingSm">
                              Product ID: {productId}
                            </Text>
                            <InlineStack gap="200">
                              <Badge status="info">
                                {variants.length} variants saved
                              </Badge>
                              <Badge status="success">
                                {variants.filter(v => v.enabled).length} enabled
                              </Badge>
                            </InlineStack>
                            <Text as="p" variant="bodySm" color="subdued">
                              Saved: {new Date(variants[0].createdAt).toLocaleDateString()}
                            </Text>
                          </BlockStack>

                          <Button
                            destructive
                            onClick={() => handleRemoveProduct(productId)}
                            loading={isLoading}
                          >
                            Remove
                          </Button>
                        </InlineStack>

                        {/* Variant Details */}
                        <Card sectioned>
                          <BlockStack gap="200">
                            <Text as="h5" variant="headingXs">
                              Variants ({variants.length})
                            </Text>
                            <BlockStack gap="100">
                              {variants.map((variant) => (
                                <InlineStack key={variant.id.toString()} align="space-between">
                                  <BlockStack gap="050">
                                    <Text as="p" variant="bodySm">
                                      Variant ID: {variant.id.toString()}
                                    </Text>
                                    <InlineStack gap="200">
                                      <Badge status={variant.enabled ? "success" : "attention"}>
                                        {variant.enabled ? "Enabled" : "Disabled"}
                                      </Badge>
                                      <Text as="span" variant="bodySm" color="subdued">
                                        Stock: {variant.inventoryLevel}
                                      </Text>
                                    </InlineStack>
                                  </BlockStack>

                                  <Button
                                    size="micro"
                                    onClick={() => handleToggleVariant(variant.id, variant.enabled)}
                                    loading={isLoading}
                                  >
                                    {variant.enabled ? "Disable" : "Enable"}
                                  </Button>
                                </InlineStack>
                              ))}
                            </BlockStack>
                          </BlockStack>
                        </Card>
                      </BlockStack>
                    </Card>
                  ))}
                </BlockStack>
              )}
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
