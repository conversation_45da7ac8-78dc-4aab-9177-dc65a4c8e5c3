import { useState, useCallback, useRef, useEffect } from "react";
import { json } from "@remix-run/node";
import { useActionData, useLoaderData, useSubmit, useNavigation } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  TextField,
  Button,
  BlockStack,
  Text,
  Box,
  Spinner,
  Banner,
  EmptyState,
  LegacyStack,
  ResourceList,
  ResourceItem
} from "@shopify/polaris";
// Icons will be handled with inline SVG or text
import { authenticate } from "../shopify.server";
import { createChatSession, getShopChatSessions, getChatSession } from "../services/chatbot";

export const loader = async ({ request }) => {
  const { session } = await authenticate.admin(request);
  
  // Check if Groq API key is configured
  const isGroqConfigured = Boolean(process.env.GROQ_API_KEY);
  
  // Get query params
  const url = new URL(request.url);
  const chatId = url.searchParams.get("id");
  
  let currentChat = null;
  let chatSessions = [];
  
  try {
    // Get chat sessions for this shop
    chatSessions = await getShopChatSessions(session.shop);
    
    // If a specific chat is requested, get its details
    if (chatId) {
      currentChat = await getChatSession(chatId);
    }
  } catch (error) {
    console.error("Error loading chat data:", error);
  }
  
  return json({
    isGroqConfigured,
    shop: session.shop,
    chatSessions,
    currentChat
  });
};

export const action = async ({ request }) => {
  const { session } = await authenticate.admin(request);
  const formData = await request.formData();
  const message = formData.get("message");
  const sessionId = formData.get("sessionId") || null;
  
  if (!process.env.GROQ_API_KEY) {
    return json({
      error: "Groq API key is not configured. Please add GROQ_API_KEY to your environment variables."
    });
  }
  
  try {
    let chatSession;
    
    if (sessionId) {
      // Get existing session
      const existingChat = await getChatSession(sessionId);
      if (!existingChat) {
        throw new Error("Chat session not found");
      }
      
      // Create a new session with the same ID
      chatSession = {
        sessionId,
        sendMessage: async (msg) => {
          const newSession = createChatSession(session.shop);
          return newSession.sendMessage(msg);
        }
      };
    } else {
      // Create new session
      chatSession = createChatSession(session.shop);
    }
    
    const response = await chatSession.sendMessage(message);
    
    return json({
      response,
      sessionId: chatSession.sessionId
    });
  } catch (error) {
    console.error("Chatbot error:", error);
    return json({
      error: "Failed to get response from Groq. Please try again later."
    });
  }
};

export default function ChatbotPage() {
  const { isGroqConfigured, shop, chatSessions, currentChat } = useLoaderData();
  const actionData = useActionData();
  const navigation = useNavigation();
  const submit = useSubmit();
  const [message, setMessage] = useState("");
  const [conversation, setConversation] = useState([]);
  const [sessionId, setSessionId] = useState("");
  const messagesEndRef = useRef(null);
  
  const isSubmitting = navigation.state === "submitting";
  
  // Initialize conversation from loaded data
  useEffect(() => {
    if (currentChat) {
      setSessionId(currentChat.id);
      setConversation(
        currentChat.messages.map(msg => ({
          role: msg.role === 'user' ? 'user' : 'assistant',
          content: msg.content
        }))
      );
    } else {
      setConversation([]);
      setSessionId("");
    }
  }, [currentChat]);
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };
  
  useEffect(() => {
    scrollToBottom();
  }, [conversation]);
  
  const handleSubmit = useCallback(() => {
    if (!message.trim() || isSubmitting) return;
    
    // Add user message to conversation
    setConversation(prev => [...prev, { role: "user", content: message }]);
    
    const formData = new FormData();
    formData.append("message", message);
    if (sessionId) {
      formData.append("sessionId", sessionId);
    }
    
    submit(formData, { method: "post" });
    setMessage("");
  }, [message, sessionId, submit, isSubmitting]);
  
  // Handle response
  useEffect(() => {
    if (actionData?.error) {
      setConversation(prev => [...prev, { role: "error", content: actionData.error }]);
      return;
    }
    
    if (actionData?.response && !conversation.find(msg => 
      msg.role === "assistant" && msg.content === actionData.response)) {
      setConversation(prev => [...prev, { role: "assistant", content: actionData.response }]);
      
      if (actionData.sessionId && !sessionId) {
        setSessionId(actionData.sessionId);
        // Update URL to include session ID without full page reload
        const url = new URL(window.location.href);
        url.searchParams.set("id", actionData.sessionId);
        window.history.pushState({}, "", url);
      }
    }
  }, [actionData, conversation, sessionId]);
  
  if (!isGroqConfigured) {
    return (
      <Page title="Shopify Chatbot">
        <Layout>
          <Layout.Section>
            <Banner
              title="Configuration Required"
              status="critical"
            >
              <p>Groq API key is not configured. Please add GROQ_API_KEY to your environment variables.</p>
            </Banner>
          </Layout.Section>
        </Layout>
      </Page>
    );
  }
  
  return (
    <Page
      title="Shopify Chatbot"
      primaryAction={{
        content: "New Chat",
        onAction: () => {
          setConversation([]);
          setSessionId("");
          const url = new URL(window.location.href);
          url.searchParams.delete("id");
          window.history.pushState({}, "", url);
        }
      }}
    >
      <Layout>
        <Layout.Section secondary>
          <Card>
            <ResourceList
              resourceName={{ singular: 'chat', plural: 'chats' }}
              items={chatSessions}
              renderItem={(item) => {
                const shortId = item.id.substring(0, 8);
                const preview = item.messages[0]?.content || "Empty conversation";
                const truncatedPreview = preview.length > 30 
                  ? preview.substring(0, 30) + "..." 
                  : preview;
                
                return (
                  <ResourceItem
                    id={item.id}
                    url={`/app/chatbot?id=${item.id}`}
                    accessibilityLabel={`View chat ${shortId}`}
                    name={`Chat ${shortId}`}
                    verticalAlignment="center"
                  >
                    <LegacyStack>
                      <LegacyStack.Item fill>
                        <h3>
                          <Text variant="bodyMd" fontWeight="bold" as="span">
                            Chat {shortId}
                          </Text>
                        </h3>
                        <div>
                          <Text variant="bodyMd" as="p" color="subdued">
                            {truncatedPreview}
                          </Text>
                        </div>
                      </LegacyStack.Item>
                    </LegacyStack>
                  </ResourceItem>
                );
              }}
              emptyState={
                <EmptyState
                  heading="No chat history yet"
                  image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
                >
                  <p>Start a new conversation using the chat panel.</p>
                </EmptyState>
              }
            />
          </Card>
        </Layout.Section>
        
        <Layout.Section>
          <Card>
            <Card.Section>
              <BlockStack gap="4">
                <div style={{ 
                  minHeight: "400px", 
                  maxHeight: "500px", 
                  overflowY: "auto",
                  padding: "10px",
                  background: "var(--p-color-bg-subdued)",
                  borderRadius: "8px"
                }}>
                  {conversation.length === 0 ? (
                    <Box padding="5" textAlign="center">
                      <div style={{ fontSize: "48px", marginBottom: "16px" }}>💬</div>
                      <Text as="p" variant="bodyMd" color="subdued">
                        Start a conversation with the Groq-powered chatbot
                      </Text>
                    </Box>
                  ) : (
                    conversation.map((msg, index) => (
                      <Box
                        key={index}
                        padding="3"
                        background={msg.role === "user" ? "bg-surface-secondary" : 
                                   msg.role === "error" ? "critical-subdued" : "bg-surface"}
                        borderRadius="2"
                        borderColor="border"
                        borderWidth="1"
                        marginBottom="3"
                        marginLeft={msg.role === "user" ? "4" : "0"}
                        marginRight={msg.role === "user" ? "0" : "4"}
                      >
                        <Text as="p">
                          <strong>{msg.role === "user" ? "You: " : 
                                  msg.role === "error" ? "Error: " : "Bot: "}</strong>
                          {msg.content}
                        </Text>
                      </Box>
                    ))
                  )}
                  {isSubmitting && (
                    <Box
                      padding="3"
                      background="bg-surface"
                      borderRadius="2"
                      borderColor="border"
                      borderWidth="1"
                      marginBottom="3"
                      marginRight="4"
                      display="flex"
                      alignItems="center"
                      gap="2"
                    >
                      <Spinner size="small" />
                      <Text as="p" variant="bodyMd">Thinking...</Text>
                    </Box>
                  )}
                  <div ref={messagesEndRef} />
                </div>
                
                <div style={{ display: "flex", gap: "8px" }}>
                  <div style={{ flexGrow: 1 }}>
                    <TextField
                      label="Message"
                      labelHidden
                      value={message}
                      onChange={setMessage}
                      autoComplete="off"
                      placeholder="Type your message here..."
                      onKeyDown={(e) => {
                        if (e.key === "Enter" && !e.shiftKey) {
                          e.preventDefault();
                          handleSubmit();
                        }
                      }}
                      disabled={isSubmitting}
                    />
                  </div>
                  <Button
                    primary
                    onClick={handleSubmit}
                    disabled={isSubmitting || !message.trim()}
                  >
                    Send ➤
                  </Button>
                </div>
              </div>
            </Card.Section>
            <Card.Section>
              <Text as="p" variant="bodyMd" color="subdued">
                Powered by Groq's LLama3-70B model for ultra-fast responses
              </Text>
            </Card.Section>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}