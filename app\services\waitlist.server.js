import prisma from "../db.server";

/**
 * Waitlist Service - Common operations using Prisma ORM
 */

export class WaitlistService {
  constructor(shop) {
    this.shop = shop;
  }

  /**
   * Create a new waitlist entry
   */
  async createWaitlistEntry({ email, productVariantId, priority = "Medium" }) {
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error("Invalid email format");
    }

    // Check for existing entry
    const existing = await this.findExistingEntry(email, productVariantId);
    if (existing) {
      throw new Error("Email already exists on waitlist for this product variant");
    }

    // Create new entry
    return await prisma.waitlist.create({
      data: {
        email,
        productVariantId: BigInt(productVariantId),
        priority,
        shop: this.shop,
      },
      include: {
        productVariant: true,
      },
    });
  }

  /**
   * Find existing waitlist entry
   */
  async findExistingEntry(email, productVariantId) {
    return await prisma.waitlist.findFirst({
      where: {
        email,
        productVariantId: BigInt(productVariantId),
        shop: this.shop,
      },
    });
  }

  /**
   * Get all waitlist entries with pagination
   */
  async getWaitlistEntries({ page = 1, limit = 10, priority, productVariantId } = {}) {
    const skip = (page - 1) * limit;
    
    const whereClause = {
      shop: this.shop,
      ...(priority && { priority }),
      ...(productVariantId && { productVariantId: BigInt(productVariantId) }),
    };

    const [entries, totalCount] = await Promise.all([
      prisma.waitlist.findMany({
        where: whereClause,
        include: {
          productVariant: true,
        },
        orderBy: [
          { priority: 'asc' }, // High priority first
          { createdAt: 'desc' }, // Newest first within same priority
        ],
        skip,
        take: limit,
      }),
      prisma.waitlist.count({
        where: whereClause,
      }),
    ]);

    return {
      entries,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalCount / limit),
        totalCount,
        hasNext: page < Math.ceil(totalCount / limit),
        hasPrevious: page > 1,
      },
    };
  }

  /**
   * Update waitlist entry
   */
  async updateWaitlistEntry(id, { email, priority }) {
    // Validate email if provided
    if (email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new Error("Invalid email format");
      }
    }

    return await prisma.waitlist.update({
      where: {
        id,
        shop: this.shop,
      },
      data: {
        ...(email && { email }),
        ...(priority && { priority }),
      },
      include: {
        productVariant: true,
      },
    });
  }

  /**
   * Delete waitlist entry
   */
  async deleteWaitlistEntry(id) {
    return await prisma.waitlist.delete({
      where: {
        id,
        shop: this.shop,
      },
    });
  }

  /**
   * Bulk create waitlist entries
   */
  async bulkCreateEntries(emails, productVariantId, priority = "Medium") {
    // Validate emails
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const validEmails = emails.filter(email => emailRegex.test(email));
    
    if (validEmails.length === 0) {
      throw new Error("No valid emails provided");
    }

    // Check for existing entries
    const existingEntries = await prisma.waitlist.findMany({
      where: {
        email: { in: validEmails },
        productVariantId: BigInt(productVariantId),
        shop: this.shop,
      },
      select: { email: true },
    });

    const existingEmails = existingEntries.map(entry => entry.email);
    const newEmails = validEmails.filter(email => !existingEmails.includes(email));

    if (newEmails.length === 0) {
      return {
        created: 0,
        duplicates: existingEmails.length,
        invalid: emails.length - validEmails.length,
      };
    }

    // Create new entries
    const createData = newEmails.map(email => ({
      email,
      productVariantId: BigInt(productVariantId),
      priority,
      shop: this.shop,
    }));

    const result = await prisma.waitlist.createMany({
      data: createData,
      skipDuplicates: true,
    });

    return {
      created: result.count,
      duplicates: existingEmails.length,
      invalid: emails.length - validEmails.length,
    };
  }

  /**
   * Get waitlist statistics
   */
  async getWaitlistStats() {
    const [
      totalEntries,
      priorityStats,
      variantStats,
      recentEntries,
    ] = await Promise.all([
      // Total entries
      prisma.waitlist.count({
        where: { shop: this.shop },
      }),

      // Priority breakdown
      prisma.waitlist.groupBy({
        by: ['priority'],
        where: { shop: this.shop },
        _count: { priority: true },
      }),

      // Top variants
      prisma.waitlist.groupBy({
        by: ['productVariantId'],
        where: { shop: this.shop },
        _count: { productVariantId: true },
        orderBy: { _count: { productVariantId: 'desc' } },
        take: 10,
      }),

      // Recent entries (last 7 days)
      prisma.waitlist.count({
        where: {
          shop: this.shop,
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          },
        },
      }),
    ]);

    return {
      totalEntries,
      recentEntries,
      priorityBreakdown: priorityStats.reduce((acc, stat) => {
        acc[stat.priority] = stat._count.priority;
        return acc;
      }, {}),
      topVariants: variantStats.map(stat => ({
        productVariantId: stat.productVariantId.toString(),
        count: stat._count.productVariantId,
      })),
    };
  }

  /**
   * Export waitlist data
   */
  async exportWaitlist({ priority, productVariantId } = {}) {
    const whereClause = {
      shop: this.shop,
      ...(priority && { priority }),
      ...(productVariantId && { productVariantId: BigInt(productVariantId) }),
    };

    const entries = await prisma.waitlist.findMany({
      where: whereClause,
      include: {
        productVariant: true,
      },
      orderBy: [
        { priority: 'asc' },
        { createdAt: 'asc' },
      ],
    });

    return entries.map(entry => ({
      id: entry.id,
      email: entry.email,
      productVariantId: entry.productVariantId.toString(),
      productId: entry.productVariant.productId.toString(),
      priority: entry.priority,
      createdAt: entry.createdAt.toISOString(),
      updatedAt: entry.updatedAt.toISOString(),
    }));
  }

  /**
   * Get waitlist entries for a specific product variant
   */
  async getEntriesForVariant(productVariantId, { priority } = {}) {
    return await prisma.waitlist.findMany({
      where: {
        shop: this.shop,
        productVariantId: BigInt(productVariantId),
        ...(priority && { priority }),
      },
      orderBy: [
        { priority: 'asc' },
        { createdAt: 'asc' },
      ],
    });
  }

  /**
   * Update priority for multiple entries
   */
  async bulkUpdatePriority(waitlistIds, priority) {
    return await prisma.waitlist.updateMany({
      where: {
        id: { in: waitlistIds },
        shop: this.shop,
      },
      data: {
        priority,
      },
    });
  }

  /**
   * Delete multiple entries
   */
  async bulkDeleteEntries(waitlistIds) {
    return await prisma.waitlist.deleteMany({
      where: {
        id: { in: waitlistIds },
        shop: this.shop,
      },
    });
  }
}

/**
 * Helper function to create a waitlist service instance
 */
export function createWaitlistService(shop) {
  return new WaitlistService(shop);
}
