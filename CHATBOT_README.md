# Shopify Chatbot App

A comprehensive AI-powered chatbot solution for Shopify stores using Groq's LLama3-70B model for ultra-fast responses.

## Features

- 🤖 **AI-Powered Conversations**: Uses Groq's LLama3-70B model for intelligent responses
- 🎨 **Customizable Widget**: Fully customizable appearance and positioning
- 📱 **Mobile Responsive**: Works seamlessly on all devices
- 🛍️ **Product Context Awareness**: Understands product pages and provides relevant assistance
- 💬 **Chat History**: Persistent conversation history across sessions
- ⚙️ **Admin Configuration**: Easy-to-use settings panel for store owners
- 🎯 **Theme Integration**: Simple snippet integration for any Shopify theme

## Setup Instructions

### 1. Environment Configuration

Add your Groq API key to your environment variables:

```bash
GROQ_API_KEY=your_groq_api_key_here
```

You can get a free Groq API key from [https://console.groq.com/](https://console.groq.com/)

### 2. Database Migration

Run the Prisma migration to create the necessary database tables:

```bash
npm run prisma migrate dev
```

### 3. Install the App

1. Deploy your app to your development store
2. Install the app from the Shopify admin
3. Navigate to the "Chatbot Settings" page to configure your widget

### 4. Theme Integration

#### Option A: Using Theme Extensions (Recommended)

The app includes theme extensions that can be enabled directly from the Shopify admin:

1. Go to your store's theme editor
2. Add the "Chatbot Widget" block to any section
3. Configure the widget settings in the theme editor

#### Option B: Manual Integration

Add the following snippet to your theme's `theme.liquid` file before the closing `</body>` tag:

```liquid
{% render 'chatbot-widget' %}
```

Then copy the `chatbot-widget.liquid` snippet from `extensions/chatbot/snippets/` to your theme's snippets folder.

### 5. Configuration

1. Go to your Shopify admin
2. Navigate to Apps > Chatbot App
3. Click on "Chatbot Settings"
4. Configure your chatbot:
   - Enable/disable the widget
   - Set the widget title and welcome message
   - Choose colors and positioning
   - Select which pages to show the widget on

## Usage

### Admin Interface

- **Chatbot Page**: Test the chatbot and view conversation history
- **Settings Page**: Configure widget appearance and behavior

### Customer Experience

Customers will see a floating chat button on your storefront. When clicked, it opens a chat interface where they can:

- Ask questions about products
- Get help with orders and shipping
- Receive general store information
- Get product recommendations

### Product Context

The chatbot automatically understands:

- Current product being viewed
- Product details (title, price, description, variants)
- Collection context
- Page type (product, collection, cart, etc.)

## API Endpoints

### `/api/chatbot` (POST)

Handles storefront chatbot requests.

**Request Body:**
```json
{
  "message": "Hello, I need help with this product",
  "sessionId": "optional-session-id",
  "context": {
    "page": "/products/example-product",
    "product": {
      "title": "Example Product",
      "price": "$29.99",
      "description": "Product description..."
    },
    "shop": "your-shop.myshopify.com"
  }
}
```

**Response:**
```json
{
  "response": "Hello! I'd be happy to help you with this product...",
  "sessionId": "generated-session-id",
  "success": true
}
```

## Customization

### Styling

The chatbot widget can be customized through the admin settings or by modifying the CSS in the theme extension files.

### AI Behavior

Modify the system prompts in `app/services/chatbot.js` to customize how the AI responds to different contexts.

### Product Integration

Enhance product context by modifying the `extractProductData()` function in `extensions/chatbot/assets/chatbot.js`.

## File Structure

```
app/
├── routes/
│   ├── app.chatbot.jsx          # Main chatbot interface
│   ├── app.chatbot.settings.jsx # Settings configuration
│   └── api.chatbot.jsx          # Storefront API endpoint
├── services/
│   ├── chatbot.js               # Core chatbot logic
│   └── metafields.js            # Shopify metafields integration
└── db.server.js                 # Database configuration

extensions/chatbot/
├── blocks/
│   └── chatbot_widget.liquid    # Theme extension block
├── assets/
│   └── chatbot.js               # Frontend JavaScript
├── snippets/
│   ├── chatbot-widget.liquid    # Manual integration snippet
│   └── stars.liquid             # Rating stars (legacy)
└── shopify.extension.toml       # Extension configuration

prisma/
└── schema.prisma                # Database schema
```

## Troubleshooting

### Chatbot Not Responding

1. Check that `GROQ_API_KEY` is set in your environment
2. Verify the API endpoint is accessible
3. Check browser console for JavaScript errors

### Widget Not Appearing

1. Ensure the theme extension is enabled or snippet is properly added
2. Check that the widget is enabled in settings
3. Verify the page matches the display criteria

### Database Issues

1. Run `npm run prisma migrate reset` to reset the database
2. Run `npm run prisma migrate dev` to apply migrations
3. Check that the database connection is working

## Support

For issues and questions:

1. Check the browser console for error messages
2. Review the server logs for API errors
3. Ensure all environment variables are properly set
4. Verify Groq API key is valid and has sufficient credits

## License

This project is part of your Shopify app and follows your app's licensing terms.
