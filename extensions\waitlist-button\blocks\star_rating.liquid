<!-- Waitlist Section -->
{% comment %} Check if we should show waitlist {% endcomment %}
{% assign current_variant = product.selected_or_first_available_variant %}
{% assign show_waitlist = false %}

{% if block.settings.show_only_out_of_stock == false %}
  {% comment %} Always show if setting is disabled {% endcomment %}
  {% assign show_waitlist = true %}
{% else %}
  {% comment %} Only show if product is out of stock {% endcomment %}
  {% if current_variant.inventory_management == 'shopify' and current_variant.inventory_quantity <= 0 %}
    {% assign show_waitlist = true %}
  {% elsif current_variant.inventory_management != 'shopify' and current_variant.available == false %}
    {% assign show_waitlist = true %}
  {% endif %}
{% endif %}

{% if show_waitlist %}
<div id="waitlist-widget" style="
  max-width: 400px;
  margin: 20px auto;
  padding: 24px;
  background: linear-gradient(135deg, {{ block.settings.background_color_start | default: '#667eea' }} 0%, {{ block.settings.background_color_end | default: '#764ba2' }} 100%);
  border-radius: {{ block.settings.border_radius | default: 12 }}px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
">
  <!-- Waitlist Header -->
  <div style="margin-bottom: 20px;">
    <h3 style="
      color: {{ block.settings.text_color | default: 'white' }};
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    ">
      {{ block.settings.heading | default: 'Join the Waitlist' }}
    </h3>
    <p style="
      color: {{ block.settings.text_color | default: 'rgba(255, 255, 255, 0.9)' | color_modify: 'alpha', 0.9 }};
      margin: 0;
      font-size: 16px;
      line-height: 1.5;
    ">
      {{ block.settings.description | default: 'Be the first to know when this item is back in stock!' }}
    </p>
  </div>

  <!-- Waitlist Form -->
  <form id="waitlist-form" style="display: flex; flex-direction: column; gap: 16px;">
    <!-- Email Input -->
    <div style="position: relative;">
      <input
        type="email"
        id="waitlist-email"
        name="email"
        placeholder="Enter your email address"
        required
        style="
          width: 100%;
          padding: 16px 20px;
          border: none;
          border-radius: 8px;
          font-size: 16px;
          background: rgba(255, 255, 255, 0.95);
          color: #333;
          box-sizing: border-box;
          transition: all 0.3s ease;
          outline: none;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        "
        onfocus="this.style.background='white'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 16px rgba(0, 0, 0, 0.15)';"
        onblur="this.style.background='rgba(255, 255, 255, 0.95)'; this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(0, 0, 0, 0.1)';"
      />
    </div>

    <!-- Join Waitlist Button -->
    <button
      type="submit"
      id="waitlist-button"
      style="
        background: {{ block.settings.button_background | default: 'white' }};
        color: {{ block.settings.button_text_color | default: '#667eea' }};
        border: none;
        padding: 16px 32px;
        border-radius: {{ block.settings.border_radius | default: 8 }}px;
        font-size: 18px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        text-transform: uppercase;
        letter-spacing: 0.5px;
      "
      onmouseover="
        this.style.background='{{ block.settings.button_background | default: '#f8f9ff' | color_lighten: 5 }}';
        this.style.transform='translateY(-2px)';
        this.style.boxShadow='0 6px 20px rgba(0, 0, 0, 0.15)';
      "
      onmouseout="
        this.style.background='{{ block.settings.button_background | default: 'white' }}';
        this.style.transform='translateY(0)';
        this.style.boxShadow='0 4px 16px rgba(0, 0, 0, 0.1)';
      "
      onmousedown="this.style.transform='translateY(0)';"
      onmouseup="this.style.transform='translateY(-2px)';"
    >
      <span id="button-text">{{ block.settings.button_text | default: 'Join the Waitlist' }}</span>
      <span id="button-loading" style="display: none;">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" style="animation: spin 1s linear infinite; margin-right: 8px;">
          <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" stroke-opacity="0.3"/>
          <path d="M12 2a10 10 0 0 1 10 10" stroke="currentColor" stroke-width="4"/>
        </svg>
        Adding...
      </span>
    </button>
  </form>

  <!-- Success Message -->
  <div id="waitlist-success" style="
    display: none;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 16px;
    margin-top: 16px;
    color: white;
    font-weight: 500;
  ">
    <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" style="color: #4ade80;">
        <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <span>{{ block.settings.success_message | default: "You're on the waitlist! We'll notify you when it's available." }}</span>
    </div>
  </div>

  <!-- Error Message -->
  <div id="waitlist-error" style="
    display: none;
    background: rgba(239, 68, 68, 0.2);
    border: 2px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
    padding: 16px;
    margin-top: 16px;
    color: white;
    font-weight: 500;
  ">
    <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" style="color: #ef4444;">
        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
        <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
        <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
      </svg>
      <span id="error-message">Something went wrong. Please try again.</span>
    </div>
  </div>
</div>

<!-- CSS Animations -->
<style>
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  #waitlist-success,
  #waitlist-error {
    animation: fadeIn 0.3s ease;
  }

  /* Mobile Responsive */
  @media (max-width: 480px) {
    .waitlist-container {
      margin: 10px;
      padding: 20px;
    }
  }
</style>

<!-- JavaScript for Waitlist Functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('waitlist-form');
  const emailInput = document.getElementById('waitlist-email');
  const button = document.getElementById('waitlist-button');
  const buttonText = document.getElementById('button-text');
  const buttonLoading = document.getElementById('button-loading');
  const successMessage = document.getElementById('waitlist-success');
  const errorMessage = document.getElementById('waitlist-error');
  const errorText = document.getElementById('error-message');

  // Get current product variant ID from Shopify
  const productVariantId = window.ShopifyAnalytics?.meta?.selectedVariantId ||
                          document.querySelector('[name="id"]')?.value ||
                          document.querySelector('select[name="id"]')?.value ||
                          '{{ product.selected_or_first_available_variant.id }}';

  console.log('Product Variant ID:', productVariantId);

  // Add event listener for form submission
  form.addEventListener('submit', async function(e) {
    e.preventDefault();

    const email = emailInput.value.trim();

    // Validate email
    if (!email || !isValidEmail(email)) {
      showError('Please enter a valid email address.');
      return;
    }

    // Show loading state
    setLoading(true);
    hideMessages();

    // Log the submission attempt
    console.log('Waitlist submission started:', {
      email: email,
      productVariantId: productVariantId,
      timestamp: new Date().toISOString()
    });

    try {
      // Make API call to your waitlist endpoint via app proxy
      const response = await fetch('/apps/wait-wise', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          action: 'create_waitlist',
          email: email,
          productVariantId: productVariantId,
          priority: '{{ block.settings.priority | default: "Medium" }}',
          shop: '{{ shop.permanent_domain }}',
          productId: '{{ product.id }}',
          productTitle: '{{ product.title | escape }}',
          variantTitle: window.ShopifyAnalytics?.meta?.selectedVariantTitle || '{{ product.selected_or_first_available_variant.title | escape }}',
          timestamp: new Date().toISOString()
        })
      });

      // Log the response for debugging
      console.log('Waitlist API response status:', response.status);
      console.log('Waitlist API response headers:', Object.fromEntries(response.headers.entries()));

      const result = await response.json();
      console.log('Waitlist API response data:', result);

      if (response.ok && result.success) {
        showSuccess();
        emailInput.value = '';

        // Fire success event for analytics
        window.dispatchEvent(new CustomEvent('waitlistJoinSuccess', {
          detail: {
            email: email,
            productVariantId: productVariantId,
            productId: '{{ product.id }}',
            waitlistId: result.waitlistId,
            timestamp: new Date().toISOString()
          }
        }));

        console.log('Waitlist submission successful:', result);
      } else {
        const errorMsg = result.message || result.error || 'Failed to join waitlist. Please try again.';
        showError(errorMsg);

        // Fire error event for analytics
        window.dispatchEvent(new CustomEvent('waitlistJoinError', {
          detail: {
            email: email,
            productVariantId: productVariantId,
            error: errorMsg,
            timestamp: new Date().toISOString()
          }
        }));

        console.error('Waitlist submission failed:', result);
      }
    } catch (error) {
      console.error('Waitlist network error:', error);
      showError('Network error. Please check your connection and try again.');

      // Fire network error event for analytics
      window.dispatchEvent(new CustomEvent('waitlistNetworkError', {
        detail: {
          email: email,
          productVariantId: productVariantId,
          error: error.message,
          timestamp: new Date().toISOString()
        }
      }));
    } finally {
      setLoading(false);
    }
  });

  // Add event listener for email input changes
  emailInput.addEventListener('input', function() {
    // Clear any existing error messages when user starts typing
    if (errorMessage.style.display !== 'none') {
      hideMessages();
    }

    // Real-time email validation feedback
    const email = emailInput.value.trim();
    if (email && !isValidEmail(email)) {
      emailInput.style.borderColor = '#ef4444';
    } else {
      emailInput.style.borderColor = '';
    }
  });

  // Add event listener for Enter key in email input
  emailInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      e.preventDefault();
      form.dispatchEvent(new Event('submit'));
    }
  });

  // Add event listener for button click (additional to form submit)
  button.addEventListener('click', function(e) {
    // Log button click for analytics
    console.log('Waitlist button clicked:', {
      productVariantId: productVariantId,
      timestamp: new Date().toISOString()
    });

    // Fire custom event for tracking
    window.dispatchEvent(new CustomEvent('waitlistButtonClicked', {
      detail: {
        productVariantId: productVariantId,
        productId: '{{ product.id }}',
        email: emailInput.value.trim()
      }
    }));
  });

  function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  function setLoading(loading) {
    button.disabled = loading;
    buttonText.style.display = loading ? 'none' : 'inline';
    buttonLoading.style.display = loading ? 'inline-flex' : 'none';

    if (loading) {
      button.style.opacity = '0.8';
      button.style.cursor = 'not-allowed';
    } else {
      button.style.opacity = '1';
      button.style.cursor = 'pointer';
    }
  }

  function showSuccess() {
    hideMessages();
    successMessage.style.display = 'block';
    form.style.display = 'none';
  }

  function showError(message) {
    hideMessages();
    errorText.textContent = message;
    errorMessage.style.display = 'block';
  }

  function hideMessages() {
    successMessage.style.display = 'none';
    errorMessage.style.display = 'none';
  }

  // Auto-hide error messages after 5 seconds
  let errorTimeout;
  function showError(message) {
    hideMessages();
    errorText.textContent = message;
    errorMessage.style.display = 'block';

    clearTimeout(errorTimeout);
    errorTimeout = setTimeout(() => {
      errorMessage.style.display = 'none';
    }, 5000);
  }
});
</script>
{% endif %}

{% comment %} Alternative message when product is in stock {% endcomment %}
{% unless show_waitlist %}
<div style="
  max-width: 400px;
  margin: 20px auto;
  padding: 20px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 12px;
  text-align: center;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
">
  <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" style="color: #4ade80;">
      <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
    <span style="font-size: 18px; font-weight: 600;">{{ block.settings.in_stock_message | default: 'In Stock - Ready to Ship!' }}</span>
  </div>
</div>
{% endunless %}

{% schema %}
{
  "name": "Waitlist Button",
  "target": "section",
  "settings": [
    {
      "type": "header",
      "content": "Waitlist Settings"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Join the Waitlist"
    },
    {
      "type": "text",
      "id": "description",
      "label": "Description",
      "default": "Be the first to know when this item is back in stock!"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button Text",
      "default": "Join the Waitlist"
    },
    {
      "type": "text",
      "id": "success_message",
      "label": "Success Message",
      "default": "You're on the waitlist! We'll notify you when it's available."
    },
    {
      "type": "text",
      "id": "in_stock_message",
      "label": "In Stock Message",
      "default": "In Stock - Ready to Ship!"
    },
    {
      "type": "select",
      "id": "priority",
      "label": "Default Priority",
      "options": [
        {
          "value": "High",
          "label": "High"
        },
        {
          "value": "Medium",
          "label": "Medium"
        },
        {
          "value": "Low",
          "label": "Low"
        }
      ],
      "default": "Medium"
    },
    {
      "type": "header",
      "content": "Style Settings"
    },
    {
      "type": "color",
      "id": "background_color_start",
      "label": "Background Gradient Start",
      "default": "#667eea"
    },
    {
      "type": "color",
      "id": "background_color_end",
      "label": "Background Gradient End",
      "default": "#764ba2"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text Color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button Background",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button Text Color",
      "default": "#667eea"
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 25,
      "step": 1,
      "unit": "px",
      "label": "Border Radius",
      "default": 12
    },
    {
      "type": "checkbox",
      "id": "show_only_out_of_stock",
      "label": "Only show when out of stock",
      "default": true
    }
  ],
  "enabled_on":{
    "templates":["product"]
  }




}


{% endschema %}