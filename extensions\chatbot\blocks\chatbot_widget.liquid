<!-- Chatbot Widget -->
<div id="shopify-chatbot-widget" class="chatbot-widget" style="display: {{ block.settings.show_widget | default: true }};">
  <div class="chatbot-toggle" id="chatbot-toggle">
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M20 2H4C2.9 2 2 2.9 2 4V22L6 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2ZM20 16H5.17L4 17.17V4H20V16Z" fill="currentColor"/>
      <path d="M7 9H17V11H7V9ZM7 12H15V14H7V12Z" fill="currentColor"/>
    </svg>
    <span class="chatbot-notification" id="chatbot-notification" style="display: none;"></span>
  </div>
  
  <div class="chatbot-container" id="chatbot-container" style="display: none;">
    <div class="chatbot-header">
      <h3>{{ block.settings.widget_title | default: 'Chat with us' }}</h3>
      <button class="chatbot-close" id="chatbot-close">&times;</button>
    </div>
    
    <div class="chatbot-messages" id="chatbot-messages">
      <div class="chatbot-message bot-message">
        <div class="message-content">
          {{ block.settings.welcome_message | default: 'Hello! How can I help you today?' }}
        </div>
      </div>
    </div>
    
    <div class="chatbot-input-container">
      <input type="text" id="chatbot-input" placeholder="{{ block.settings.input_placeholder | default: 'Type your message...' }}" maxlength="500">
      <button id="chatbot-send" disabled>
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M2.01 21L23 12L2.01 3L2 10L17 12L2 14L2.01 21Z" fill="currentColor"/>
        </svg>
      </button>
    </div>
    
    <div class="chatbot-typing" id="chatbot-typing" style="display: none;">
      <div class="typing-indicator">
        <span></span>
        <span></span>
        <span></span>
      </div>
      <span>Assistant is typing...</span>
    </div>
  </div>
</div>

<style>
  .chatbot-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  .chatbot-toggle {
    width: 60px;
    height: 60px;
    background: {{ block.settings.primary_color | default: '#007cba' }};
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    color: white;
    position: relative;
  }
  
  .chatbot-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }
  
  .chatbot-notification {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4444;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }
  
  .chatbot-container {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 350px;
    height: 500px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    animation: slideUp 0.3s ease;
  }
  
  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .chatbot-header {
    background: {{ block.settings.primary_color | default: '#007cba' }};
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .chatbot-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
  }
  
  .chatbot-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .chatbot-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .chatbot-message {
    max-width: 80%;
    animation: fadeIn 0.3s ease;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .bot-message {
    align-self: flex-start;
  }
  
  .user-message {
    align-self: flex-end;
  }
  
  .message-content {
    padding: 12px 16px;
    border-radius: 18px;
    font-size: 14px;
    line-height: 1.4;
    word-wrap: break-word;
  }
  
  .bot-message .message-content {
    background: #f1f3f4;
    color: #333;
  }
  
  .user-message .message-content {
    background: {{ block.settings.primary_color | default: '#007cba' }};
    color: white;
  }
  
  .chatbot-input-container {
    padding: 16px 20px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    gap: 12px;
    align-items: center;
  }
  
  #chatbot-input {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 20px;
    padding: 10px 16px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s ease;
  }
  
  #chatbot-input:focus {
    border-color: {{ block.settings.primary_color | default: '#007cba' }};
  }
  
  #chatbot-send {
    background: {{ block.settings.primary_color | default: '#007cba' }};
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    transition: all 0.2s ease;
  }
  
  #chatbot-send:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
  
  #chatbot-send:not(:disabled):hover {
    transform: scale(1.1);
  }
  
  .chatbot-typing {
    padding: 12px 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #666;
  }
  
  .typing-indicator {
    display: flex;
    gap: 4px;
  }
  
  .typing-indicator span {
    width: 6px;
    height: 6px;
    background: #666;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
  }
  
  .typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
  .typing-indicator span:nth-child(2) { animation-delay: -0.16s; }
  
  @keyframes typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
  }
  
  @media (max-width: 480px) {
    .chatbot-container {
      width: calc(100vw - 40px);
      height: 70vh;
      bottom: 80px;
      right: 20px;
      left: 20px;
    }
  }
</style>

<script src="{{ 'chatbot.js' | asset_url }}" defer></script>
<script>
  window.ShopifyChatbot = window.ShopifyChatbot || {};

  // Store widget configuration
  window.ShopifyChatbot.config = {
    apiEndpoint: '{{ shop.permanent_domain }}/apps/chatbot/api',
    shopDomain: '{{ shop.permanent_domain }}',
    widgetTitle: '{{ block.settings.widget_title | default: "Chat with us" }}',
    welcomeMessage: '{{ block.settings.welcome_message | default: "Hello! How can I help you today?" }}',
    primaryColor: '{{ block.settings.primary_color | default: "#007cba" }}'
  };
</script>

{% schema %}
{
  "name": "Chatbot Widget",
  "target": "section",
  "settings": [
    {
      "type": "checkbox",
      "id": "show_widget",
      "label": "Show Chatbot Widget",
      "default": true
    },
    {
      "type": "text",
      "id": "widget_title",
      "label": "Widget Title",
      "default": "Chat with us"
    },
    {
      "type": "textarea",
      "id": "welcome_message",
      "label": "Welcome Message",
      "default": "Hello! How can I help you today?"
    },
    {
      "type": "text",
      "id": "input_placeholder",
      "label": "Input Placeholder",
      "default": "Type your message..."
    },
    {
      "type": "color",
      "id": "primary_color",
      "label": "Primary Color",
      "default": "#007cba"
    }
  ]
}
{% endschema %}
