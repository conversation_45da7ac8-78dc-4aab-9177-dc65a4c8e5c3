// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

// Note that some adapters may set a maximum length for the String type by default, please ensure your strings are long
// enough when changing adapters.
// See https://www.prisma.io/docs/orm/reference/prisma-schema-reference#string for more information
datasource db {
  provider = "sqlite"
  url      = "file:dev.sqlite"
}

model Session {
  id            String    @id
  shop          String
  state         String
  isOnline      Boolean   @default(false)
  scope         String?
  expires       DateTime?
  accessToken   String
  userId        BigInt?
  firstName     String?
  lastName      String?
  email         String?
  accountOwner  Boolean   @default(false)
  locale        String?
  collaborator  <PERSON><PERSON><PERSON>?  @default(false)
  emailVerified Boolean?  @default(false)
}

model Store {
  shop String @id
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  waitlists Waitlist[]
  productVariants ProductVariant[]
}

model ProductVariant {
  id BigInt @id
  enabled Boolean @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  inventoryLevel Int @default(0)
  productId BigInt
  shop String
  store Store @relation(fields: [shop], references: [shop])
  waitlists Waitlist[]
}

model Waitlist {
  id String @id @default(uuid())
  email String
  productVariantId BigInt
  priority Priority @default(Medium)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  shop String
  store Store @relation(fields: [shop], references: [shop])
  productVariant ProductVariant @relation(fields: [productVariantId], references: [id])
}

enum Priority {
  High
  Medium
  Low
}


