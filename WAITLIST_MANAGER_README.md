# 📋 Waitlist Manager - Prisma ORM Implementation

A comprehensive waitlist management system built with Prisma ORM for Shopify apps, featuring full CRUD operations, bulk actions, and advanced filtering.

## 🗄️ **Database Schema**

### **Waitlist Model**
```prisma
model Waitlist {
  id String @id @default(uuid())
  email String
  productVariantId BigInt
  priority Priority @default(Medium)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  shop String
  store Store @relation(fields: [shop], references: [shop])
  productVariant ProductVariant @relation(fields: [productVariantId], references: [id])
}

enum Priority {
  High
  Medium
  Low
}
```

### **Relationships**
- **Store**: Each waitlist belongs to a specific Shopify store
- **ProductVariant**: Each waitlist entry is for a specific product variant
- **Priority**: Enum with High, Medium, Low values

## 🚀 **Features**

### ✅ **Core CRUD Operations**
- **Create** - Add new waitlist entries with validation
- **Read** - View all waitlist entries with pagination
- **Update** - Edit email and priority of existing entries
- **Delete** - Remove individual waitlist entries

### ✅ **Advanced Features**
- **Pagination** - Handle large datasets efficiently
- **Email Validation** - Ensure valid email formats
- **Duplicate Prevention** - Prevent duplicate email/variant combinations
- **Priority Management** - High, Medium, Low priority levels
- **Product Integration** - Links to ProductVariant model

### ✅ **Bulk Operations (API)**
- **Bulk Create** - Add multiple emails at once
- **Bulk Update** - Change priority for multiple entries
- **Bulk Delete** - Remove multiple entries
- **Export** - Export waitlist data with filtering
- **Statistics** - Get comprehensive waitlist analytics

## 📁 **File Structure**

```
app/routes/
├── app.waitlist.jsx          # Main waitlist management UI
└── api.waitlist.jsx          # Bulk operations API

Documentation/
└── WAITLIST_MANAGER_README.md # This file
```

## 🔧 **Prisma ORM Operations**

### **1. Create Waitlist Entry**
```javascript
const newWaitlist = await prisma.waitlist.create({
  data: {
    email: "<EMAIL>",
    productVariantId: BigInt(123456789),
    priority: "High",
    shop: "mystore.myshopify.com",
  },
  include: {
    productVariant: true,
  },
});
```

### **2. Find with Relationships**
```javascript
const waitlists = await prisma.waitlist.findMany({
  where: {
    shop: session.shop,
  },
  include: {
    productVariant: true,
    store: true,
  },
  orderBy: {
    createdAt: 'desc',
  },
});
```

### **3. Update Entry**
```javascript
const updated = await prisma.waitlist.update({
  where: {
    id: waitlistId,
    shop: session.shop,
  },
  data: {
    email: newEmail,
    priority: newPriority,
  },
});
```

### **4. Delete Entry**
```javascript
await prisma.waitlist.delete({
  where: {
    id: waitlistId,
    shop: session.shop,
  },
});
```

### **5. Bulk Operations**
```javascript
// Bulk create
await prisma.waitlist.createMany({
  data: emailArray.map(email => ({
    email,
    productVariantId: BigInt(variantId),
    priority: "Medium",
    shop: shopDomain,
  })),
  skipDuplicates: true,
});

// Bulk update
await prisma.waitlist.updateMany({
  where: {
    id: { in: waitlistIds },
    shop: shopDomain,
  },
  data: {
    priority: newPriority,
  },
});
```

### **6. Advanced Queries**
```javascript
// Count by priority
const stats = await prisma.waitlist.groupBy({
  by: ['priority'],
  where: { shop: shopDomain },
  _count: {
    priority: true,
  },
});

// Find duplicates
const existing = await prisma.waitlist.findFirst({
  where: {
    email: customerEmail,
    productVariantId: BigInt(variantId),
    shop: shopDomain,
  },
});
```

## 🎯 **Usage Guide**

### **Step 1: Access Waitlist Manager**
1. Navigate to `/app/waitlist` in your Shopify app
2. Or click "Waitlist Manager" in the navigation menu

### **Step 2: Create Waitlist Entry**
1. Click "Add to Waitlist" button
2. Enter customer email address
3. Select product variant from dropdown
4. Choose priority level (High/Medium/Low)
5. Click "Create" to save

### **Step 3: Manage Entries**
1. View all entries in the data table
2. Use pagination for large datasets
3. Edit entries by clicking "Edit" button
4. Delete entries with "Delete" button

### **Step 4: Bulk Operations (API)**
```javascript
// Bulk create via API
fetch('/api/waitlist', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'bulk_create',
    data: {
      emails: ['<EMAIL>', '<EMAIL>'],
      productVariantId: '123456789',
      priority: 'High'
    }
  })
});
```

## 🔒 **Data Validation & Security**

### **Email Validation**
```javascript
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
if (!emailRegex.test(email)) {
  throw new Error("Invalid email format");
}
```

### **Duplicate Prevention**
```javascript
const existing = await prisma.waitlist.findFirst({
  where: {
    email: email,
    productVariantId: BigInt(productVariantId),
    shop: session.shop,
  },
});

if (existing) {
  throw new Error("Email already on waitlist");
}
```

### **Shop Isolation**
All queries include shop filter to ensure data isolation:
```javascript
where: {
  shop: session.shop, // Always filter by shop
  // ... other conditions
}
```

## 📊 **API Endpoints**

### **POST /api/waitlist**

#### **Bulk Create**
```json
{
  "action": "bulk_create",
  "data": {
    "emails": ["<EMAIL>", "<EMAIL>"],
    "productVariantId": "123456789",
    "priority": "High"
  }
}
```

#### **Bulk Update Priority**
```json
{
  "action": "bulk_update_priority",
  "data": {
    "waitlistIds": ["uuid1", "uuid2"],
    "priority": "High"
  }
}
```

#### **Export Waitlist**
```json
{
  "action": "export_waitlist",
  "data": {
    "productVariantId": "123456789",
    "priority": "High"
  }
}
```

#### **Get Statistics**
```json
{
  "action": "get_stats"
}
```

## 🎨 **UI Components**

### **Main Features**
- **DataTable** - Displays waitlist entries with sorting
- **Modal Form** - Create/edit waitlist entries
- **Pagination** - Navigate through large datasets
- **Priority Badges** - Visual priority indicators
- **Action Buttons** - Edit/delete operations

### **Form Validation**
- Email format validation
- Required field validation
- Duplicate entry prevention
- Product variant selection

## 📈 **Performance Optimizations**

### **Database Indexing**
```prisma
// Recommended indexes
@@index([shop, productVariantId])
@@index([shop, email])
@@index([shop, priority])
@@index([shop, createdAt])
```

### **Pagination**
- Limit: 10 entries per page
- Skip/Take for efficient querying
- Total count for pagination controls

### **Bulk Operations**
- `createMany` for efficient bulk inserts
- `updateMany` for bulk updates
- `skipDuplicates` to handle conflicts

## 🔄 **Data Flow**

```
1. User opens Waitlist Manager
   ↓
2. Loader fetches waitlists with pagination
   ↓
3. User clicks "Add to Waitlist"
   ↓
4. Modal opens with form
   ↓
5. User fills form and submits
   ↓
6. Action validates and creates entry
   ↓
7. Database updated via Prisma
   ↓
8. UI refreshes with new data
   ↓
9. Success message displayed
```

## 🎉 **Benefits**

- ✅ **Type Safety** - Full TypeScript support with Prisma
- ✅ **Relationship Management** - Automatic joins and includes
- ✅ **Query Optimization** - Efficient database operations
- ✅ **Data Validation** - Built-in validation and constraints
- ✅ **Scalability** - Handles large datasets with pagination
- ✅ **Security** - Shop isolation and input validation
- ✅ **Developer Experience** - Auto-generated types and IntelliSense

The Waitlist Manager provides a complete solution for managing customer waitlists with the power of Prisma ORM! 🚀
