# 🛍️ Product Selector Page

A comprehensive Shopify app page that allows users to select products using Shopify App Bridge and save them to the database with full variant management.

## 🚀 Features

### ✅ **Product Selection**
- **Shopify App Bridge Integration** - Uses native Shopify resource picker
- **Product Filtering** - Only shows active, non-draft products
- **Variant Support** - Automatically includes all product variants
- **Visual Preview** - Shows product image, title, and details

### ✅ **Database Management**
- **Save Products** - Store selected products and variants in your database
- **Variant Control** - Enable/disable individual variants
- **Inventory Tracking** - Track inventory levels for each variant
- **Bulk Operations** - Remove entire products with all variants

### ✅ **User Interface**
- **Modern Design** - Built with Shopify Polaris components
- **Real-time Updates** - Instant feedback on all actions
- **Loading States** - Clear loading indicators for all operations
- **Error Handling** - Comprehensive error messages and recovery

## 📁 File Structure

```
app/routes/app.product-selector.jsx
├── Loader Function (Data Fetching)
├── Action Function (Database Operations)
└── React Component (UI)
```

## 🔧 Technical Implementation

### **Database Schema**
The page uses the `ProductVariant` model:

```prisma
model ProductVariant {
  id BigInt @id
  enabled Boolean @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  inventoryLevel Int @default(0)
  productId BigInt
  shop String
  store Store @relation(fields: [shop], references: [shop])
  waitlists Waitlist[]
}
```

### **Key Functions**

#### **1. Product Selection (`handleProductSelect`)**
```javascript
const selection = await shopify.resourcePicker({
  type: "product",
  action: "select",
  multiple: false,
  filter: {
    variants: true,
    draft: false,
    archived: false,
  },
});
```

#### **2. Save to Database (`save_product` action)**
- Extracts product and variant data
- Uses `upsert` to handle existing products
- Saves all variants with inventory levels
- Returns success/error feedback

#### **3. Variant Management (`toggle_variant` action)**
- Enable/disable individual variants
- Updates database in real-time
- Provides instant UI feedback

#### **4. Product Removal (`remove_product` action)**
- Removes all variants for a product
- Cascading delete from database
- Confirms successful removal

## 🎯 Usage Guide

### **Step 1: Select a Product**
1. Click "Select Product" button
2. Shopify resource picker opens
3. Browse and select any product
4. Product details appear on screen

### **Step 2: Save to Database**
1. Review selected product details
2. Click "Save to Database"
3. All variants are saved automatically
4. Success message confirms save

### **Step 3: Manage Variants**
1. View saved products in the list
2. See all variants with their status
3. Enable/disable individual variants
4. Monitor inventory levels

### **Step 4: Remove Products**
1. Find product in saved list
2. Click "Remove" button
3. All variants are deleted
4. Confirmation message appears

## 🔄 API Actions

### **GET (Loader)**
```javascript
// Fetches saved products for current shop
const savedProducts = await prisma.productVariant.findMany({
  where: { shop: session.shop },
  orderBy: { createdAt: 'desc' }
});
```

### **POST Actions**

#### **Save Product**
```javascript
formData.append("action", "save_product");
formData.append("productData", JSON.stringify(selectedProduct));
```

#### **Toggle Variant**
```javascript
formData.append("action", "toggle_variant");
formData.append("variantId", variantId.toString());
formData.append("enabled", enabled.toString());
```

#### **Remove Product**
```javascript
formData.append("action", "remove_product");
formData.append("productId", productId);
```

## 🎨 UI Components

### **Product Selection Card**
- Primary action button for product picker
- Save button (appears after selection)
- Error/success banners
- Loading states

### **Selected Product Preview**
- Product thumbnail
- Title and ID display
- Variant count and status badges
- Detailed variant information

### **Saved Products List**
- Grouped by product ID
- Variant details with controls
- Enable/disable toggles
- Remove product buttons

## 🔒 Security Features

- **Shop Isolation** - All queries filtered by shop domain
- **Session Validation** - Requires authenticated Shopify session
- **Input Validation** - All form data validated before processing
- **Error Handling** - Graceful error recovery and user feedback

## 📊 Data Flow

```
1. User clicks "Select Product"
   ↓
2. Shopify App Bridge opens resource picker
   ↓
3. User selects product from Shopify
   ↓
4. Product data returned to app
   ↓
5. User clicks "Save to Database"
   ↓
6. Action function processes variants
   ↓
7. Data saved to ProductVariant table
   ↓
8. UI updates with success message
   ↓
9. Product appears in saved list
```

## 🚀 Getting Started

1. **Navigate to Product Selector**
   - Go to `/app/product-selector` in your app
   - Or click "Product Selector" in navigation

2. **Select Your First Product**
   - Click "Select Product" button
   - Choose any product from your store
   - Review the product details

3. **Save and Manage**
   - Save the product to your database
   - Enable/disable variants as needed
   - Remove products when no longer needed

## 🔧 Customization Options

### **Modify Product Filters**
```javascript
filter: {
  variants: true,
  draft: false,
  archived: false,
  // Add more filters as needed
}
```

### **Add Custom Fields**
Extend the `ProductVariant` model to include additional fields:
```prisma
model ProductVariant {
  // ... existing fields
  customField String?
  priority Int @default(1)
}
```

### **Custom UI Components**
Replace or extend Polaris components with your own styling and functionality.

## 🎉 Benefits

- ✅ **Native Integration** - Uses official Shopify App Bridge
- ✅ **Real-time Sync** - Always up-to-date with your store
- ✅ **Scalable Design** - Handles stores with thousands of products
- ✅ **User Friendly** - Intuitive interface for all skill levels
- ✅ **Extensible** - Easy to add new features and customizations

The Product Selector page provides a solid foundation for any Shopify app that needs to work with product data! 🎊
