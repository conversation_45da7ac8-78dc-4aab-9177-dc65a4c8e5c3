import { authenticate } from "../shopify.server";

/**
 * Sync chatbot settings to Shopify metafields
 * This allows the theme to access chatbot configuration
 */
export async function syncChatbotSettingsToMetafields(shopId, settings) {
  try {
    // Get the admin API client
    const { admin } = await authenticate.admin({ shop: shopId });
    
    // Prepare metafield data
    const metafieldData = {
      namespace: "chatbot",
      key: "settings",
      value: JSON.stringify({
        enabled: settings.enabled,
        widget_title: settings.widgetTitle,
        welcome_message: settings.welcomeMessage,
        input_placeholder: settings.inputPlaceholder,
        primary_color: settings.primaryColor,
        position: settings.position,
        show_on_pages: settings.showOnPages
      }),
      type: "json"
    };
    
    // Create or update the metafield
    const response = await admin.graphql(`
      mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
        metafieldsSet(metafields: $metafields) {
          metafields {
            id
            namespace
            key
            value
          }
          userErrors {
            field
            message
          }
        }
      }
    `, {
      variables: {
        metafields: [{
          ownerId: `gid://shopify/Shop/${shopId}`,
          ...metafieldData
        }]
      }
    });
    
    const result = await response.json();
    
    if (result.data?.metafieldsSet?.userErrors?.length > 0) {
      console.error("Error setting metafields:", result.data.metafieldsSet.userErrors);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error("Error syncing chatbot settings to metafields:", error);
    return false;
  }
}

/**
 * Get product information for chatbot context
 */
export async function getProductContext(shopId, productId) {
  try {
    const { admin } = await authenticate.admin({ shop: shopId });
    
    const response = await admin.graphql(`
      query getProduct($id: ID!) {
        product(id: $id) {
          id
          title
          description
          vendor
          productType
          tags
          priceRangeV2 {
            minVariantPrice {
              amount
              currencyCode
            }
            maxVariantPrice {
              amount
              currencyCode
            }
          }
          variants(first: 10) {
            edges {
              node {
                id
                title
                price
                availableForSale
                selectedOptions {
                  name
                  value
                }
              }
            }
          }
          images(first: 5) {
            edges {
              node {
                url
                altText
              }
            }
          }
          seo {
            title
            description
          }
        }
      }
    `, {
      variables: {
        id: `gid://shopify/Product/${productId}`
      }
    });
    
    const result = await response.json();
    return result.data?.product || null;
  } catch (error) {
    console.error("Error fetching product context:", error);
    return null;
  }
}

/**
 * Get collection information for chatbot context
 */
export async function getCollectionContext(shopId, collectionId) {
  try {
    const { admin } = await authenticate.admin({ shop: shopId });
    
    const response = await admin.graphql(`
      query getCollection($id: ID!) {
        collection(id: $id) {
          id
          title
          description
          productsCount
          products(first: 10) {
            edges {
              node {
                id
                title
                priceRangeV2 {
                  minVariantPrice {
                    amount
                    currencyCode
                  }
                }
              }
            }
          }
        }
      }
    `, {
      variables: {
        id: `gid://shopify/Collection/${collectionId}`
      }
    });
    
    const result = await response.json();
    return result.data?.collection || null;
  } catch (error) {
    console.error("Error fetching collection context:", error);
    return null;
  }
}

/**
 * Search products for chatbot recommendations
 */
export async function searchProducts(shopId, query, limit = 5) {
  try {
    const { admin } = await authenticate.admin({ shop: shopId });
    
    const response = await admin.graphql(`
      query searchProducts($query: String!, $first: Int!) {
        products(first: $first, query: $query) {
          edges {
            node {
              id
              title
              handle
              description
              vendor
              priceRangeV2 {
                minVariantPrice {
                  amount
                  currencyCode
                }
              }
              images(first: 1) {
                edges {
                  node {
                    url
                    altText
                  }
                }
              }
            }
          }
        }
      }
    `, {
      variables: {
        query: query,
        first: limit
      }
    });
    
    const result = await response.json();
    return result.data?.products?.edges?.map(edge => edge.node) || [];
  } catch (error) {
    console.error("Error searching products:", error);
    return [];
  }
}
