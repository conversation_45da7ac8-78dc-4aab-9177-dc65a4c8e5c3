<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopify AI Chatbot Widget</title>
    <style>
        /* Chatbot Widget Styles */
        .shopify-chatbot-widget {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 9999;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        }

        .chatbot-toggle {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            color: white;
            position: relative;
            border: none;
            outline: none;
        }

        .chatbot-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
        }

        .chatbot-toggle.active {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }

        .chatbot-notification {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: none;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .chatbot-container {
            position: absolute;
            bottom: 80px;
            right: 0;
            width: 350px;
            height: 500px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            display: none;
            flex-direction: column;
            overflow: hidden;
            animation: slideUp 0.3s ease;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .chatbot-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chatbot-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .chatbot-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.2s ease;
        }

        .chatbot-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .chatbot-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
            background: #f8f9fa;
        }

        .chatbot-message {
            max-width: 85%;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .bot-message {
            align-self: flex-start;
        }

        .user-message {
            align-self: flex-end;
        }

        .message-content {
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.5;
            word-wrap: break-word;
            position: relative;
        }

        .bot-message .message-content {
            background: white;
            color: #333;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .user-message .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 4px;
        }

        .chatbot-input-container {
            padding: 20px;
            border-top: 1px solid #e9ecef;
            background: white;
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .chatbot-input {
            flex: 1;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            padding: 12px 16px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.2s ease;
            resize: none;
            min-height: 20px;
            max-height: 100px;
            font-family: inherit;
        }

        .chatbot-input:focus {
            border-color: #667eea;
        }

        .chatbot-send {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: white;
            transition: all 0.2s ease;
        }

        .chatbot-send:disabled {
            background: #dee2e6;
            cursor: not-allowed;
        }

        .chatbot-send:not(:disabled):hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .chatbot-typing {
            padding: 12px 20px;
            display: none;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #6c757d;
            background: #f8f9fa;
        }

        .typing-indicator {
            display: flex;
            gap: 4px;
        }

        .typing-indicator span {
            width: 8px;
            height: 8px;
            background: #6c757d;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
        .typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .welcome-message {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }

        .welcome-message .icon {
            font-size: 48px;
            margin-bottom: 16px;
            display: block;
        }

        /* Mobile Responsive */
        @media (max-width: 480px) {
            .shopify-chatbot-widget {
                bottom: 10px;
                right: 10px;
                left: 10px;
            }
            
            .chatbot-container {
                width: 100%;
                height: 70vh;
                bottom: 80px;
                right: 0;
                left: 0;
                border-radius: 16px 16px 0 0;
            }
        }

        /* Error message styles */
        .error-message .message-content {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <!-- Chatbot Widget HTML -->
    <div id="shopify-chatbot-widget" class="shopify-chatbot-widget">
        <button class="chatbot-toggle" id="chatbot-toggle" aria-label="Open chat">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 2H4C2.9 2 2 2.9 2 4V22L6 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2ZM20 16H5.17L4 17.17V4H20V16Z" fill="currentColor"/>
                <path d="M7 9H17V11H7V9ZM7 12H15V14H7V12Z" fill="currentColor"/>
            </svg>
            <span class="chatbot-notification" id="chatbot-notification">1</span>
        </button>
        
        <div class="chatbot-container" id="chatbot-container">
            <div class="chatbot-header">
                <h3 id="chatbot-title">Chat with us</h3>
                <button class="chatbot-close" id="chatbot-close" aria-label="Close chat">&times;</button>
            </div>
            
            <div class="chatbot-messages" id="chatbot-messages">
                <div class="welcome-message">
                    <span class="icon">🤖</span>
                    <div>
                        <strong>Hello! I'm your AI assistant</strong><br>
                        <span id="welcome-text">How can I help you today?</span>
                    </div>
                </div>
            </div>
            
            <div class="chatbot-typing" id="chatbot-typing">
                <div class="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <span>AI is thinking...</span>
            </div>
            
            <div class="chatbot-input-container">
                <textarea 
                    id="chatbot-input" 
                    class="chatbot-input"
                    placeholder="Type your message..." 
                    rows="1"
                    maxlength="500"
                ></textarea>
                <button id="chatbot-send" class="chatbot-send" disabled aria-label="Send message">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M2.01 21L23 12L2.01 3L2 10L17 12L2 14L2.01 21Z" fill="currentColor"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Chatbot Widget JavaScript will be added in the next file
        console.log('Shopify Chatbot Widget Loaded');
    </script>
</body>
</html>
