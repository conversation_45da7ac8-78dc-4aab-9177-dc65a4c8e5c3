import { json } from "@remix-run/node";
import prisma from "../db.server";

export const action = async ({ request }) => {
  // Set CORS headers for cross-origin requests from Shopify storefront
  const headers = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, X-Requested-With",
  };

  // Handle preflight OPTIONS request
  if (request.method === "OPTIONS") {
    return new Response(null, { status: 200, headers });
  }

  if (request.method !== "POST") {
    return Response.json({ error: "Method not allowed" }, { status: 405, headers });
  }

  try {
    const body = await request.json();
    const { action, data } = body;

    if (action === "create_waitlist") {
      return await handleCreateWaitlist(data, headers, request);
    }

    return Response.json({ error: "Invalid action" }, { status: 400, headers });
  } catch (error) {
    console.error("Public waitlist API error:", error);
    return Response.json({ 
      error: "Internal server error",
      message: error.message 
    }, { status: 500, headers });
  }
};

// <PERSON>le creating a waitlist entry from the storefront
async function handleCreateWaitlist(data, headers, request = null) {
  const { email, productVariantId, priority = "Medium", shop } = data;

  // Validate required fields
  if (!email || !productVariantId) {
    return Response.json({
      error: "Email and product variant ID are required"
    }, { status: 400, headers });
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return Response.json({
      error: "Please enter a valid email address"
    }, { status: 400, headers });
  }

  try {
    // Extract shop domain from referrer or use provided shop
    const referrer = request?.headers?.get("referer") || "";
    const shopDomain = shop || extractShopFromUrl(referrer);

    if (!shopDomain) {
      return Response.json({ 
        error: "Shop domain could not be determined" 
      }, { status: 400, headers });
    }

    // Check if the product variant exists in our database
    const productVariant = await prisma.productVariant.findFirst({
      where: {
        id: BigInt(productVariantId),
        shop: shopDomain,
      },
    });

    if (!productVariant) {
      return Response.json({ 
        error: "Product variant not found. Please contact support." 
      }, { status: 404, headers });
    }

    // Check if email is already on waitlist for this variant
    const existingEntry = await prisma.waitlist.findFirst({
      where: {
        email: email,
        productVariantId: BigInt(productVariantId),
        shop: shopDomain,
      },
    });

    if (existingEntry) {
      return Response.json({ 
        success: true,
        message: "You're already on the waitlist for this product!",
        alreadyExists: true
      }, { headers });
    }

    // Create new waitlist entry
    const newWaitlist = await prisma.waitlist.create({
      data: {
        email: email,
        productVariantId: BigInt(productVariantId),
        priority: priority,
        shop: shopDomain,
      },
    });

    return Response.json({
      success: true,
      message: "Successfully added to waitlist! We'll notify you when it's available.",
      waitlistId: newWaitlist.id
    }, { headers });

  } catch (error) {
    console.error("Error creating waitlist entry:", error);
    return Response.json({ 
      error: "Failed to join waitlist. Please try again." 
    }, { status: 500, headers });
  }
}

// Extract shop domain from URL
function extractShopFromUrl(url) {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname;
    
    // Handle myshopify.com domains
    if (hostname.includes('.myshopify.com')) {
      return hostname;
    }
    
    // Handle custom domains - you might need to add logic here
    // to map custom domains to shop domains
    return hostname;
  } catch (error) {
    console.error("Error extracting shop from URL:", error);
    return null;
  }
}

// GET method for health check
export const loader = async () => {
  return Response.json({ 
    status: "ok", 
    service: "waitlist-api",
    timestamp: new Date().toISOString()
  });
};
